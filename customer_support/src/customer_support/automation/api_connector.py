"""API connectors for external helpdesk systems."""

import httpx
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog

from ..core.models import ActionResult, Ticket, TicketUpdate, Message

logger = structlog.get_logger(__name__)


class APIConnectorError(Exception):
    """Base exception for API connector errors."""
    pass


class BaseAPIConnector:
    """Base class for API connectors."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize API connector."""
        self.config = config
        self.logger = logger.bind(connector=self.__class__.__name__)
        
        # API configuration
        self.base_url = config.get("api_url")
        self.api_key = config.get("api_key")
        self.username = config.get("username")
        self.password = config.get("password")
        self.timeout = config.get("timeout", 30)
        self.headers = config.get("headers", {})
        
        # HTTP client
        self.client: Optional[httpx.AsyncClient] = None
    
    async def connect(self) -> bool:
        """Connect to the API."""
        try:
            # Setup authentication
            auth = None
            auth_headers = {}
            
            if self.api_key:
                auth_headers["Authorization"] = f"Bearer {self.api_key}"
            elif self.username and self.password:
                auth = httpx.BasicAuth(self.username, self.password)
            
            # Merge headers
            headers = {**self.headers, **auth_headers}
            
            # Create client
            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                headers=headers,
                auth=auth,
                timeout=self.timeout
            )
            
            # Test connection
            await self._test_connection()
            
            self.logger.info("Connected to API", base_url=self.base_url)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to API", error=str(e))
            raise APIConnectorError(f"API connection failed: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the API."""
        if self.client:
            try:
                await self.client.aclose()
                self.logger.info("Disconnected from API")
            except Exception as e:
                self.logger.error("Error disconnecting from API", error=str(e))
            finally:
                self.client = None
    
    async def _test_connection(self) -> None:
        """Test the API connection."""
        # Override in subclasses for specific health check endpoints
        pass
    
    async def get_ticket(self, ticket_id: str) -> ActionResult:
        """Get ticket details from the API."""
        if not self.client:
            return ActionResult(
                success=False,
                error_message="Not connected to API"
            )
        
        start_time = datetime.now()
        
        try:
            response = await self.client.get(f"/tickets/{ticket_id}")
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Retrieved ticket", ticket_id=ticket_id, execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data=response.json(),
                execution_time=execution_time
            )
            
        except httpx.HTTPStatusError as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to get ticket", 
                            ticket_id=ticket_id, 
                            status_code=e.response.status_code,
                            error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"API request failed: {e.response.status_code}",
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Error getting ticket", ticket_id=ticket_id, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Request failed: {e}",
                execution_time=execution_time
            )
    
    async def update_ticket(self, ticket_id: str, update_data: Dict[str, Any]) -> ActionResult:
        """Update ticket via API."""
        if not self.client:
            return ActionResult(
                success=False,
                error_message="Not connected to API"
            )
        
        start_time = datetime.now()
        
        try:
            response = await self.client.put(f"/tickets/{ticket_id}", json=update_data)
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Updated ticket", 
                           ticket_id=ticket_id, 
                           update_data=update_data,
                           execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data=response.json(),
                execution_time=execution_time
            )
            
        except httpx.HTTPStatusError as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to update ticket", 
                            ticket_id=ticket_id, 
                            status_code=e.response.status_code,
                            error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"API request failed: {e.response.status_code}",
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Error updating ticket", ticket_id=ticket_id, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Request failed: {e}",
                execution_time=execution_time
            )
    
    async def post_reply(self, ticket_id: str, reply_text: str, is_public: bool = True) -> ActionResult:
        """Post a reply to a ticket via API."""
        if not self.client:
            return ActionResult(
                success=False,
                error_message="Not connected to API"
            )
        
        start_time = datetime.now()
        
        try:
            reply_data = {
                "body": reply_text,
                "public": is_public,
                "author_id": self.config.get("agent_id")
            }
            
            response = await self.client.post(f"/tickets/{ticket_id}/comments", json=reply_data)
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Posted reply", 
                           ticket_id=ticket_id, 
                           reply_length=len(reply_text),
                           is_public=is_public,
                           execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data=response.json(),
                execution_time=execution_time
            )
            
        except httpx.HTTPStatusError as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to post reply", 
                            ticket_id=ticket_id, 
                            status_code=e.response.status_code,
                            error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"API request failed: {e.response.status_code}",
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Error posting reply", ticket_id=ticket_id, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Request failed: {e}",
                execution_time=execution_time
            )


class ZendeskConnector(BaseAPIConnector):
    """Zendesk API connector."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Zendesk connector."""
        # Set Zendesk-specific defaults
        zendesk_config = {
            "headers": {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        }
        
        merged_config = {**zendesk_config, **config}
        super().__init__(merged_config)
    
    async def _test_connection(self) -> None:
        """Test Zendesk API connection."""
        response = await self.client.get("/api/v2/users/me.json")
        response.raise_for_status()
    
    async def get_ticket(self, ticket_id: str) -> ActionResult:
        """Get Zendesk ticket."""
        if not self.client:
            return ActionResult(success=False, error_message="Not connected to API")
        
        start_time = datetime.now()
        
        try:
            response = await self.client.get(f"/api/v2/tickets/{ticket_id}.json")
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return ActionResult(
                success=True,
                output_data=response.json()["ticket"],
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return ActionResult(
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
    
    async def update_ticket(self, ticket_id: str, update_data: Dict[str, Any]) -> ActionResult:
        """Update Zendesk ticket."""
        if not self.client:
            return ActionResult(success=False, error_message="Not connected to API")
        
        start_time = datetime.now()
        
        try:
            payload = {"ticket": update_data}
            response = await self.client.put(f"/api/v2/tickets/{ticket_id}.json", json=payload)
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return ActionResult(
                success=True,
                output_data=response.json()["ticket"],
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return ActionResult(
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
    
    async def post_reply(self, ticket_id: str, reply_text: str, is_public: bool = True) -> ActionResult:
        """Post reply to Zendesk ticket."""
        if not self.client:
            return ActionResult(success=False, error_message="Not connected to API")
        
        start_time = datetime.now()
        
        try:
            payload = {
                "ticket": {
                    "comment": {
                        "body": reply_text,
                        "public": is_public
                    }
                }
            }
            
            response = await self.client.put(f"/api/v2/tickets/{ticket_id}.json", json=payload)
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return ActionResult(
                success=True,
                output_data=response.json()["ticket"],
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return ActionResult(
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )


class FreshdeskConnector(BaseAPIConnector):
    """Freshdesk API connector."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Freshdesk connector."""
        super().__init__(config)
    
    async def _test_connection(self) -> None:
        """Test Freshdesk API connection."""
        response = await self.client.get("/api/v2/agents/me")
        response.raise_for_status()
    
    async def post_reply(self, ticket_id: str, reply_text: str, is_public: bool = True) -> ActionResult:
        """Post reply to Freshdesk ticket."""
        if not self.client:
            return ActionResult(success=False, error_message="Not connected to API")
        
        start_time = datetime.now()
        
        try:
            payload = {
                "body": reply_text,
                "private": not is_public
            }
            
            response = await self.client.post(f"/api/v2/tickets/{ticket_id}/notes", json=payload)
            response.raise_for_status()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return ActionResult(
                success=True,
                output_data=response.json(),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return ActionResult(
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
