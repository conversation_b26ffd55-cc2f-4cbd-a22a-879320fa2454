"""Browser automation using <PERSON><PERSON><PERSON><PERSON><PERSON> and Playwright."""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from browseruse import BrowserUse

from ..core.models import ActionResult, ActionType

logger = structlog.get_logger(__name__)


class BrowserAutomationError(Exception):
    """Base exception for browser automation errors."""
    pass


class BrowserAutomator:
    """Handles browser automation for ticket management."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize browser automator."""
        self.config = config
        self.logger = logger.bind(component="browser_automator")
        
        # Browser configuration
        self.headless = config.get("headless", True)
        self.timeout = config.get("timeout", 30000)
        self.viewport = config.get("viewport", {"width": 1280, "height": 720})
        self.user_agent = config.get("user_agent")
        
        # Browser instances
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # BrowserUse integration
        self.browser_use: Optional[BrowserUse] = None
    
    async def start(self) -> None:
        """Start the browser automation system."""
        try:
            # Initialize Playwright
            self.playwright = await async_playwright().start()
            
            # Launch browser
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=["--no-sandbox", "--disable-dev-shm-usage"]
            )
            
            # Create context
            context_options = {
                "viewport": self.viewport,
                "ignore_https_errors": True,
            }
            
            if self.user_agent:
                context_options["user_agent"] = self.user_agent
            
            self.context = await self.browser.new_context(**context_options)
            
            # Create page
            self.page = await self.context.new_page()
            
            # Set default timeout
            self.page.set_default_timeout(self.timeout)
            
            # Initialize BrowserUse
            self.browser_use = BrowserUse(
                page=self.page,
                config={
                    "timeout": self.timeout,
                    "wait_for_network_idle": True,
                    "screenshot_on_error": True
                }
            )
            
            self.logger.info("Browser automation started successfully")
            
        except Exception as e:
            self.logger.error("Failed to start browser automation", error=str(e))
            await self.stop()
            raise BrowserAutomationError(f"Failed to start browser: {e}")
    
    async def stop(self) -> None:
        """Stop the browser automation system."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            self.logger.info("Browser automation stopped")
            
        except Exception as e:
            self.logger.error("Error stopping browser automation", error=str(e))
        finally:
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            self.browser_use = None
    
    async def navigate_to_url(self, url: str) -> ActionResult:
        """Navigate to a specific URL."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            await self.page.goto(url, wait_until="networkidle")
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Navigated to URL", url=url, execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data={"url": url, "title": await self.page.title()},
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to navigate to URL", url=url, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Navigation failed: {e}",
                execution_time=execution_time
            )
    
    async def login(self, login_url: str, username: str, password: str, 
                   username_selector: str = "input[name='username']",
                   password_selector: str = "input[name='password']",
                   submit_selector: str = "input[type='submit']") -> ActionResult:
        """Perform login to a website."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            # Navigate to login page
            await self.page.goto(login_url, wait_until="networkidle")
            
            # Fill username
            await self.page.fill(username_selector, username)
            
            # Fill password
            await self.page.fill(password_selector, password)
            
            # Click submit
            await self.page.click(submit_selector)
            
            # Wait for navigation
            await self.page.wait_for_load_state("networkidle")
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            # Check if login was successful (basic check)
            current_url = self.page.url
            if current_url != login_url:
                self.logger.info("Login successful", login_url=login_url, execution_time=execution_time)
                return ActionResult(
                    success=True,
                    output_data={"logged_in": True, "current_url": current_url},
                    execution_time=execution_time
                )
            else:
                return ActionResult(
                    success=False,
                    error_message="Login failed - still on login page",
                    execution_time=execution_time
                )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Login failed", login_url=login_url, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Login failed: {e}",
                execution_time=execution_time
            )
    
    async def extract_ticket_data(self, ticket_url: str, 
                                 selectors: Dict[str, str]) -> ActionResult:
        """Extract ticket data from a webpage."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            # Navigate to ticket page
            await self.page.goto(ticket_url, wait_until="networkidle")
            
            # Extract data using selectors
            extracted_data = {}
            for field, selector in selectors.items():
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        extracted_data[field] = await element.text_content()
                    else:
                        extracted_data[field] = None
                except Exception as e:
                    self.logger.warning("Failed to extract field", field=field, selector=selector, error=str(e))
                    extracted_data[field] = None
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Extracted ticket data", ticket_url=ticket_url, execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data=extracted_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to extract ticket data", ticket_url=ticket_url, error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Data extraction failed: {e}",
                execution_time=execution_time
            )
    
    async def update_ticket_status(self, ticket_url: str, new_status: str,
                                  status_selector: str, submit_selector: str) -> ActionResult:
        """Update ticket status on a webpage."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            # Navigate to ticket page
            await self.page.goto(ticket_url, wait_until="networkidle")
            
            # Select new status
            await self.page.select_option(status_selector, new_status)
            
            # Submit the change
            await self.page.click(submit_selector)
            
            # Wait for the update to complete
            await self.page.wait_for_load_state("networkidle")
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Updated ticket status", 
                           ticket_url=ticket_url, 
                           new_status=new_status, 
                           execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data={"status_updated": True, "new_status": new_status},
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to update ticket status", 
                            ticket_url=ticket_url, 
                            new_status=new_status, 
                            error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Status update failed: {e}",
                execution_time=execution_time
            )
    
    async def post_reply(self, ticket_url: str, reply_text: str,
                        reply_selector: str, submit_selector: str) -> ActionResult:
        """Post a reply to a ticket."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            # Navigate to ticket page
            await self.page.goto(ticket_url, wait_until="networkidle")
            
            # Fill reply text
            await self.page.fill(reply_selector, reply_text)
            
            # Submit the reply
            await self.page.click(submit_selector)
            
            # Wait for the reply to be posted
            await self.page.wait_for_load_state("networkidle")
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Posted reply to ticket", 
                           ticket_url=ticket_url, 
                           reply_length=len(reply_text),
                           execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data={"reply_posted": True, "reply_length": len(reply_text)},
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to post reply", 
                            ticket_url=ticket_url, 
                            error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Reply posting failed: {e}",
                execution_time=execution_time
            )
    
    async def take_screenshot(self, path: Optional[str] = None) -> ActionResult:
        """Take a screenshot of the current page."""
        if not self.page:
            return ActionResult(
                success=False,
                error_message="Browser not initialized"
            )
        
        start_time = datetime.now()
        
        try:
            if not path:
                path = f"screenshot_{int(datetime.now().timestamp())}.png"
            
            await self.page.screenshot(path=path, full_page=True)
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Screenshot taken", path=path, execution_time=execution_time)
            
            return ActionResult(
                success=True,
                output_data={"screenshot_path": path},
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to take screenshot", error=str(e))
            
            return ActionResult(
                success=False,
                error_message=f"Screenshot failed: {e}",
                execution_time=execution_time
            )
