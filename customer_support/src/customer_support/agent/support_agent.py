"""Main customer support agent with reasoning and action capabilities."""

import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog

from .gemini_client import GeminiClient
from ..core.models import (
    Ticket, TicketStatus, Message, Action, ActionCreate, ActionResult, ActionType, SearchQuery
)
from ..knowledge.knowledge_base import KnowledgeBase
from ..tools.registry import ToolRegistry
from ..safety.guardrails import SafetyManager

logger = structlog.get_logger(__name__)


class SupportAgentError(Exception):
    """Base exception for support agent errors."""
    pass


class SupportAgent:
    """AI-powered customer support agent."""
    
    def __init__(self, 
                 gemini_client: GeminiClient,
                 knowledge_base: KnowledgeBase,
                 tool_registry: ToolRegistry,
                 safety_manager: SafetyManager,
                 config: Dict[str, Any]):
        """Initialize the support agent."""
        self.gemini_client = gemini_client
        self.knowledge_base = knowledge_base
        self.tool_registry = tool_registry
        self.safety_manager = safety_manager
        self.config = config
        self.logger = logger.bind(component="support_agent")
        
        # Agent configuration
        self.max_actions_per_ticket = config.get("max_actions_per_ticket", 10)
        self.context_window_size = config.get("context_window_size", 5)
        self.knowledge_search_limit = config.get("knowledge_search_limit", 5)
        self.confidence_threshold = config.get("confidence_threshold", 0.7)
    
    async def process_ticket(self, ticket: Ticket, 
                           conversation_history: Optional[List[Message]] = None) -> Dict[str, Any]:
        """Process a ticket and determine appropriate actions."""
        self.logger.info("Processing ticket", ticket_id=ticket.id, title=ticket.title)
        
        try:
            # Step 1: Gather context from knowledge base
            knowledge_context = await self._gather_knowledge_context(ticket)
            
            # Step 2: Analyze the ticket
            analysis = await self.gemini_client.analyze_ticket(
                ticket.dict(),
                knowledge_context
            )
            
            # Step 3: Determine if we can handle this automatically
            can_auto_handle = await self._can_auto_handle(ticket, analysis)
            
            # Step 4: Generate action plan
            action_plan = await self._generate_action_plan(
                ticket, analysis, knowledge_context, conversation_history
            )
            
            # Step 5: Execute actions if approved
            execution_results = []
            if can_auto_handle:
                execution_results = await self._execute_action_plan(ticket, action_plan)
            
            result = {
                "ticket_id": ticket.id,
                "analysis": analysis,
                "knowledge_context": knowledge_context,
                "can_auto_handle": can_auto_handle,
                "action_plan": action_plan,
                "execution_results": execution_results,
                "processed_at": datetime.utcnow().isoformat()
            }
            
            self.logger.info("Ticket processing completed", 
                           ticket_id=ticket.id,
                           can_auto_handle=can_auto_handle,
                           actions_planned=len(action_plan),
                           actions_executed=len(execution_results))
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to process ticket", ticket_id=ticket.id, error=str(e))
            raise SupportAgentError(f"Ticket processing failed: {e}")
    
    async def compose_reply(self, ticket: Ticket, 
                          conversation_history: Optional[List[Message]] = None) -> str:
        """Compose a reply to a customer ticket."""
        try:
            # Gather knowledge context
            knowledge_context = await self._gather_knowledge_context(ticket)
            
            # Compose reply using Gemini
            reply = await self.gemini_client.compose_reply(
                ticket.dict(),
                knowledge_context,
                [msg.dict() for msg in conversation_history] if conversation_history else None
            )
            
            self.logger.info("Composed reply", ticket_id=ticket.id, reply_length=len(reply))
            return reply
            
        except Exception as e:
            self.logger.error("Failed to compose reply", ticket_id=ticket.id, error=str(e))
            return "Thank you for contacting us. We are reviewing your request and will respond shortly."
    
    async def _gather_knowledge_context(self, ticket: Ticket) -> str:
        """Gather relevant context from the knowledge base."""
        try:
            # Search for relevant documents
            search_query = SearchQuery(
                query=f"{ticket.title} {ticket.description}",
                limit=self.knowledge_search_limit,
                threshold=0.6
            )
            
            search_results = await self.knowledge_base.search(search_query)
            
            # Build context string
            context_parts = []
            for result in search_results:
                context_parts.append(f"Document: {result.document.title}")
                context_parts.append(f"Content: {result.document.content[:500]}...")
                context_parts.append(f"Relevance: {result.relevance}")
                context_parts.append("---")
            
            context = "\n".join(context_parts) if context_parts else "No relevant knowledge found."
            
            self.logger.info("Gathered knowledge context", 
                           ticket_id=ticket.id,
                           documents_found=len(search_results))
            
            return context
            
        except Exception as e:
            self.logger.error("Failed to gather knowledge context", ticket_id=ticket.id, error=str(e))
            return "Knowledge context unavailable."
    
    async def _can_auto_handle(self, ticket: Ticket, analysis: Dict[str, Any]) -> bool:
        """Determine if a ticket can be handled automatically."""
        try:
            # Check confidence level
            confidence = analysis.get("confidence", 0.0)
            if confidence < self.confidence_threshold:
                return False
            
            # Check complexity
            complexity = analysis.get("complexity", "medium")
            if complexity == "complex":
                return False
            
            # Check urgency
            urgency = analysis.get("urgency", 3)
            if urgency >= 5:  # Critical issues need human attention
                return False
            
            # Check category restrictions
            category = analysis.get("category", "unknown")
            restricted_categories = self.config.get("restricted_categories", ["billing", "legal"])
            if category in restricted_categories:
                return False
            
            # Check safety manager
            is_safe = await self.safety_manager.is_action_safe(
                "auto_handle_ticket",
                {"ticket_id": ticket.id, "analysis": analysis}
            )
            
            return is_safe
            
        except Exception as e:
            self.logger.error("Error determining auto-handle capability", error=str(e))
            return False
    
    async def _generate_action_plan(self, ticket: Ticket, analysis: Dict[str, Any],
                                  knowledge_context: str, 
                                  conversation_history: Optional[List[Message]] = None) -> List[Dict[str, Any]]:
        """Generate an action plan for the ticket."""
        try:
            # Get available actions from tool registry
            available_actions = self.tool_registry.list_tools()
            
            # Get action suggestions from Gemini
            suggestions = await self.gemini_client.suggest_actions(
                ticket.dict(),
                analysis,
                available_actions
            )
            
            # Build action plan
            action_plan = []
            for suggestion in suggestions[:3]:  # Limit to top 3 actions
                action_name = suggestion.get("action")
                if action_name in available_actions:
                    action_plan.append({
                        "action": action_name,
                        "priority": suggestion.get("priority", 3),
                        "reason": suggestion.get("reason", ""),
                        "expected_outcome": suggestion.get("expected_outcome", ""),
                        "requires_approval": self._requires_approval(action_name, ticket, analysis)
                    })
            
            # Add default reply action if no other actions
            if not action_plan:
                action_plan.append({
                    "action": "compose_reply",
                    "priority": 1,
                    "reason": "Provide response to customer",
                    "expected_outcome": "Customer receives helpful response",
                    "requires_approval": False
                })
            
            return action_plan
            
        except Exception as e:
            self.logger.error("Failed to generate action plan", error=str(e))
            return []
    
    async def _execute_action_plan(self, ticket: Ticket, 
                                 action_plan: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute the action plan for a ticket."""
        execution_results = []
        
        for action_item in action_plan:
            try:
                action_name = action_item["action"]
                requires_approval = action_item.get("requires_approval", False)
                
                # Check if action requires approval
                if requires_approval:
                    approved = await self.safety_manager.request_approval(
                        action_name,
                        {"ticket_id": ticket.id, "action_item": action_item}
                    )
                    if not approved:
                        execution_results.append({
                            "action": action_name,
                            "status": "pending_approval",
                            "message": "Action requires human approval"
                        })
                        continue
                
                # Execute the action
                result = await self._execute_single_action(ticket, action_name, action_item)
                execution_results.append({
                    "action": action_name,
                    "status": "executed" if result.success else "failed",
                    "result": result.dict(),
                    "message": result.error_message if not result.success else "Action completed successfully"
                })
                
            except Exception as e:
                self.logger.error("Failed to execute action", 
                                action=action_item.get("action"), 
                                error=str(e))
                execution_results.append({
                    "action": action_item.get("action"),
                    "status": "error",
                    "message": str(e)
                })
        
        return execution_results
    
    async def _execute_single_action(self, ticket: Ticket, action_name: str, 
                                   action_item: Dict[str, Any]) -> ActionResult:
        """Execute a single action."""
        try:
            # Get the tool from registry
            tool = self.tool_registry.get_tool(action_name)
            if not tool:
                return ActionResult(
                    success=False,
                    error_message=f"Tool '{action_name}' not found"
                )
            
            # Prepare action parameters
            params = {
                "ticket": ticket,
                "action_item": action_item
            }
            
            # Execute the tool
            result = await tool.execute(params)
            
            self.logger.info("Executed action", 
                           ticket_id=ticket.id,
                           action=action_name,
                           success=result.success)
            
            return result
            
        except Exception as e:
            self.logger.error("Action execution failed", 
                            action=action_name, 
                            error=str(e))
            return ActionResult(
                success=False,
                error_message=f"Action execution failed: {e}"
            )
    
    def _requires_approval(self, action_name: str, ticket: Ticket, 
                         analysis: Dict[str, Any]) -> bool:
        """Determine if an action requires human approval."""
        # High-risk actions always require approval
        high_risk_actions = ["escalate_ticket", "close_ticket", "refund_request"]
        if action_name in high_risk_actions:
            return True
        
        # High urgency tickets require approval
        urgency = analysis.get("urgency", 3)
        if urgency >= 4:
            return True
        
        # High-value customers require approval
        if ticket.metadata and ticket.metadata.get("customer_tier") == "premium":
            return True
        
        return False
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics."""
        return {
            "agent_version": "1.0.0",
            "max_actions_per_ticket": self.max_actions_per_ticket,
            "confidence_threshold": self.confidence_threshold,
            "knowledge_search_limit": self.knowledge_search_limit,
            "available_tools": len(self.tool_registry.list_tools()),
            "status": "active"
        }
