"""Gemini API client for LLM operations."""

import google.generativeai as genai
from typing import Dict, Any, List, Optional
import structlog
from datetime import datetime

logger = structlog.get_logger(__name__)


class GeminiClientError(Exception):
    """Base exception for Gemini client errors."""
    pass


class GeminiClient:
    """Client for interacting with Google's Gemini API."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini client."""
        self.config = config
        self.logger = logger.bind(component="gemini_client")
        
        # Configuration
        self.api_key = config.get("api_key")
        self.model_name = config.get("model_name", "gemini-pro")
        self.temperature = config.get("temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 2048)
        self.top_p = config.get("top_p", 0.8)
        self.top_k = config.get("top_k", 40)
        
        # Initialize client
        self.model = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Gemini client."""
        try:
            if not self.api_key:
                raise GeminiClientError("Gemini API key not provided")
            
            # Configure the API
            genai.configure(api_key=self.api_key)
            
            # Initialize the model
            generation_config = genai.types.GenerationConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                top_p=self.top_p,
                top_k=self.top_k
            )
            
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config=generation_config
            )
            
            self.logger.info("Gemini client initialized", model_name=self.model_name)
            
        except Exception as e:
            self.logger.error("Failed to initialize Gemini client", error=str(e))
            raise GeminiClientError(f"Client initialization failed: {e}")
    
    async def generate_response(self, prompt: str, context: Optional[str] = None) -> str:
        """Generate a response using Gemini."""
        if not self.model:
            raise GeminiClientError("Gemini client not initialized")
        
        start_time = datetime.now()
        
        try:
            # Prepare the full prompt
            full_prompt = prompt
            if context:
                full_prompt = f"Context:\n{context}\n\nQuery:\n{prompt}"
            
            # Generate response
            response = self.model.generate_content(full_prompt)
            
            if not response.text:
                raise GeminiClientError("Empty response from Gemini")
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            self.logger.info("Generated response", 
                           prompt_length=len(prompt),
                           response_length=len(response.text),
                           execution_time=execution_time)
            
            return response.text.strip()
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Failed to generate response", 
                            error=str(e),
                            execution_time=execution_time)
            raise GeminiClientError(f"Response generation failed: {e}")
    
    async def analyze_ticket(self, ticket_data: Dict[str, Any], 
                           knowledge_context: Optional[str] = None) -> Dict[str, Any]:
        """Analyze a ticket and provide insights."""
        try:
            # Prepare analysis prompt
            prompt = f"""
            Analyze the following customer support ticket and provide insights:
            
            Title: {ticket_data.get('title', 'N/A')}
            Description: {ticket_data.get('description', 'N/A')}
            Customer: {ticket_data.get('customer_email', 'N/A')}
            Priority: {ticket_data.get('priority', 'N/A')}
            Source: {ticket_data.get('source', 'N/A')}
            
            Please provide:
            1. Ticket category/classification
            2. Urgency assessment (1-5 scale)
            3. Complexity assessment (simple/medium/complex)
            4. Estimated resolution time
            5. Required expertise/department
            6. Key issues identified
            7. Recommended next actions
            
            Format your response as JSON with the following structure:
            {{
                "category": "string",
                "urgency": number,
                "complexity": "string",
                "estimated_resolution_time": "string",
                "required_expertise": "string",
                "key_issues": ["string"],
                "recommended_actions": ["string"],
                "confidence": number
            }}
            """
            
            response = await self.generate_response(prompt, knowledge_context)
            
            # Try to parse as JSON (basic parsing)
            try:
                import json
                # Extract JSON from response if it's wrapped in text
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                    analysis = json.loads(json_str)
                else:
                    # Fallback to structured text parsing
                    analysis = self._parse_analysis_response(response)
            except:
                analysis = self._parse_analysis_response(response)
            
            self.logger.info("Analyzed ticket", ticket_id=ticket_data.get('id'))
            return analysis
            
        except Exception as e:
            self.logger.error("Failed to analyze ticket", error=str(e))
            return {
                "category": "unknown",
                "urgency": 3,
                "complexity": "medium",
                "estimated_resolution_time": "unknown",
                "required_expertise": "general",
                "key_issues": ["analysis_failed"],
                "recommended_actions": ["manual_review"],
                "confidence": 0.1
            }
    
    async def compose_reply(self, ticket_data: Dict[str, Any], 
                          knowledge_context: Optional[str] = None,
                          conversation_history: Optional[List[Dict[str, Any]]] = None) -> str:
        """Compose a reply to a customer ticket."""
        try:
            # Prepare conversation context
            conversation_text = ""
            if conversation_history:
                for msg in conversation_history[-5:]:  # Last 5 messages
                    sender = msg.get('sender', 'unknown')
                    content = msg.get('content', '')
                    conversation_text += f"{sender}: {content}\n"
            
            # Prepare reply prompt
            prompt = f"""
            You are a helpful customer support agent. Compose a professional and helpful reply to the following customer ticket:
            
            Ticket Details:
            Title: {ticket_data.get('title', 'N/A')}
            Description: {ticket_data.get('description', 'N/A')}
            Customer: {ticket_data.get('customer_name', 'Customer')}
            Priority: {ticket_data.get('priority', 'N/A')}
            
            {f"Previous Conversation:\n{conversation_text}" if conversation_text else ""}
            
            Guidelines:
            - Be professional, empathetic, and helpful
            - Address the customer's specific concerns
            - Provide clear, actionable solutions when possible
            - Ask for clarification if needed
            - Keep the tone friendly but professional
            - If you cannot resolve the issue, explain next steps
            
            Compose a reply that addresses the customer's needs:
            """
            
            response = await self.generate_response(prompt, knowledge_context)
            
            self.logger.info("Composed reply", 
                           ticket_id=ticket_data.get('id'),
                           reply_length=len(response))
            
            return response
            
        except Exception as e:
            self.logger.error("Failed to compose reply", error=str(e))
            return "Thank you for contacting us. We have received your request and will get back to you shortly."
    
    async def suggest_actions(self, ticket_data: Dict[str, Any], 
                            analysis: Dict[str, Any],
                            available_actions: List[str]) -> List[Dict[str, Any]]:
        """Suggest actions to take for a ticket."""
        try:
            actions_text = ", ".join(available_actions)
            
            prompt = f"""
            Based on the following ticket analysis, suggest the most appropriate actions to take:
            
            Ticket: {ticket_data.get('title', 'N/A')}
            Category: {analysis.get('category', 'unknown')}
            Urgency: {analysis.get('urgency', 3)}
            Complexity: {analysis.get('complexity', 'medium')}
            Key Issues: {', '.join(analysis.get('key_issues', []))}
            
            Available Actions: {actions_text}
            
            Suggest up to 3 actions in order of priority. For each action, provide:
            1. Action name (must be from available actions)
            2. Priority (1-3, where 1 is highest)
            3. Reason for the action
            4. Expected outcome
            
            Format as JSON array:
            [
                {{
                    "action": "action_name",
                    "priority": number,
                    "reason": "string",
                    "expected_outcome": "string"
                }}
            ]
            """
            
            response = await self.generate_response(prompt)
            
            # Parse JSON response
            try:
                import json
                start_idx = response.find('[')
                end_idx = response.rfind(']') + 1
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                    suggestions = json.loads(json_str)
                else:
                    suggestions = []
            except:
                suggestions = []
            
            self.logger.info("Suggested actions", 
                           ticket_id=ticket_data.get('id'),
                           suggestions_count=len(suggestions))
            
            return suggestions
            
        except Exception as e:
            self.logger.error("Failed to suggest actions", error=str(e))
            return []
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse analysis response when JSON parsing fails."""
        # Basic text parsing fallback
        analysis = {
            "category": "general",
            "urgency": 3,
            "complexity": "medium",
            "estimated_resolution_time": "1-2 hours",
            "required_expertise": "general",
            "key_issues": [],
            "recommended_actions": [],
            "confidence": 0.5
        }
        
        # Try to extract some information from text
        response_lower = response.lower()
        
        # Category detection
        if any(word in response_lower for word in ["technical", "bug", "error"]):
            analysis["category"] = "technical"
        elif any(word in response_lower for word in ["billing", "payment", "invoice"]):
            analysis["category"] = "billing"
        elif any(word in response_lower for word in ["account", "login", "password"]):
            analysis["category"] = "account"
        
        # Urgency detection
        if any(word in response_lower for word in ["urgent", "critical", "emergency"]):
            analysis["urgency"] = 5
        elif any(word in response_lower for word in ["high", "important"]):
            analysis["urgency"] = 4
        elif any(word in response_lower for word in ["low", "minor"]):
            analysis["urgency"] = 2
        
        return analysis
