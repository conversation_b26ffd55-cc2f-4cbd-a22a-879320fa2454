"""Configuration settings for the customer support agent."""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings # NEW



class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Gemini API Configuration
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    gemini_model: str = Field("gemini-pro", env="GEMINI_MODEL")
    
    # Database Configuration
    database_url: str = Field("sqlite:///./data/customer_support.db", env="DATABASE_URL")
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    
    # Vector Database Configuration
    vector_db_path: str = Field("./data/vector_db", env="VECTOR_DB_PATH")
    embeddings_model: str = Field("all-MiniLM-L6-v2", env="EMBEDDINGS_MODEL")
    
    # Browser Automation Configuration
    browseruse_headless: bool = Field(True, env="BROWSERUSE_HEADLESS")
    browseruse_timeout: int = Field(30000, env="BROWSERUSE_TIMEOUT")
    playwright_browsers_path: str = Field("./browsers", env="PLAYWRIGHT_BROWSERS_PATH")
    
    # Email Configuration
    email_imap_server: Optional[str] = Field(None, env="EMAIL_IMAP_SERVER")
    email_imap_port: int = Field(993, env="EMAIL_IMAP_PORT")
    email_username: Optional[str] = Field(None, env="EMAIL_USERNAME")
    email_password: Optional[str] = Field(None, env="EMAIL_PASSWORD")
    email_use_ssl: bool = Field(True, env="EMAIL_USE_SSL")
    
    # API Configuration
    api_host: str = Field("0.0.0.0", env="API_HOST")
    api_port: int = Field(8000, env="API_PORT")
    api_workers: int = Field(4, env="API_WORKERS")
    api_reload: bool = Field(False, env="API_RELOAD")
    
    # Security Configuration
    secret_key: str = Field(..., env="SECRET_KEY")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    algorithm: str = Field("HS256", env="ALGORITHM")
    
    # Monitoring Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    prometheus_port: int = Field(9090, env="PROMETHEUS_PORT")
    enable_telemetry: bool = Field(True, env="ENABLE_TELEMETRY")
    
    # Safety Configuration
    enable_human_approval: bool = Field(True, env="ENABLE_HUMAN_APPROVAL")
    max_actions_per_ticket: int = Field(10, env="MAX_ACTIONS_PER_TICKET")
    rate_limit_per_minute: int = Field(60, env="RATE_LIMIT_PER_MINUTE")
    
    # External Services
    helpdesk_api_url: Optional[str] = Field(None, env="HELPDESK_API_URL")
    helpdesk_api_key: Optional[str] = Field(None, env="HELPDESK_API_KEY")
    helpdesk_username: Optional[str] = Field(None, env="HELPDESK_USERNAME")
    helpdesk_password: Optional[str] = Field(None, env="HELPDESK_PASSWORD")
    
    # Knowledge Base Configuration
    kb_update_interval: int = Field(3600, env="KB_UPDATE_INTERVAL")
    kb_max_documents: int = Field(10000, env="KB_MAX_DOCUMENTS")
    kb_chunk_size: int = Field(1000, env="KB_CHUNK_SIZE")
    kb_chunk_overlap: int = Field(200, env="KB_CHUNK_OVERLAP")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings."""
    return settings
