"""Core data models for the customer support system."""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class TicketStatus(str, Enum):
    """Ticket status enumeration."""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING_CUSTOMER = "pending_customer"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ESCALATED = "escalated"


class TicketPriority(str, Enum):
    """Ticket priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketSource(str, Enum):
    """Ticket source enumeration."""
    EMAIL = "email"
    WEB_FORM = "web_form"
    API = "api"
    HELPDESK = "helpdesk"
    CHAT = "chat"
    PHONE = "phone"


class ActionType(str, Enum):
    """Agent action type enumeration."""
    READ_TICKET = "read_ticket"
    SEARCH_KNOWLEDGE = "search_knowledge"
    COMPOSE_REPLY = "compose_reply"
    UPDATE_TICKET = "update_ticket"
    ESCALATE_TICKET = "escalate_ticket"
    BROWSER_ACTION = "browser_action"
    API_CALL = "api_call"


# SQLAlchemy Models
class TicketModel(Base):
    """Database model for tickets."""
    __tablename__ = "tickets"
    
    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, index=True)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    status = Column(String, default=TicketStatus.OPEN)
    priority = Column(String, default=TicketPriority.MEDIUM)
    source = Column(String, nullable=False)
    customer_email = Column(String, nullable=False)
    customer_name = Column(String)
    assigned_agent = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = Column(DateTime)
    metadata = Column(JSON)
    
    # Relationships
    messages = relationship("MessageModel", back_populates="ticket")
    actions = relationship("ActionModel", back_populates="ticket")


class MessageModel(Base):
    """Database model for ticket messages."""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    ticket_id = Column(Integer, ForeignKey("tickets.id"))
    sender = Column(String, nullable=False)  # customer, agent, system
    content = Column(Text, nullable=False)
    is_internal = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON)
    
    # Relationships
    ticket = relationship("TicketModel", back_populates="messages")


class ActionModel(Base):
    """Database model for agent actions."""
    __tablename__ = "actions"
    
    id = Column(Integer, primary_key=True, index=True)
    ticket_id = Column(Integer, ForeignKey("tickets.id"))
    action_type = Column(String, nullable=False)
    description = Column(Text)
    input_data = Column(JSON)
    output_data = Column(JSON)
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    execution_time = Column(Integer)  # milliseconds
    requires_approval = Column(Boolean, default=False)
    approved = Column(Boolean)
    approved_by = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    ticket = relationship("TicketModel", back_populates="actions")


class KnowledgeDocumentModel(Base):
    """Database model for knowledge base documents."""
    __tablename__ = "knowledge_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    source = Column(String)
    category = Column(String)
    tags = Column(JSON)
    embedding_id = Column(String, unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    metadata = Column(JSON)


# Pydantic Models
class TicketBase(BaseModel):
    """Base ticket model."""
    title: str
    description: str
    customer_email: str
    customer_name: Optional[str] = None
    priority: TicketPriority = TicketPriority.MEDIUM
    source: TicketSource
    metadata: Optional[Dict[str, Any]] = None


class TicketCreate(TicketBase):
    """Model for creating tickets."""
    external_id: Optional[str] = None


class TicketUpdate(BaseModel):
    """Model for updating tickets."""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TicketStatus] = None
    priority: Optional[TicketPriority] = None
    assigned_agent: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class Ticket(TicketBase):
    """Complete ticket model."""
    id: int
    external_id: Optional[str] = None
    status: TicketStatus
    assigned_agent: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MessageBase(BaseModel):
    """Base message model."""
    sender: str
    content: str
    is_internal: bool = False
    metadata: Optional[Dict[str, Any]] = None


class MessageCreate(MessageBase):
    """Model for creating messages."""
    ticket_id: int


class Message(MessageBase):
    """Complete message model."""
    id: int
    ticket_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class ActionBase(BaseModel):
    """Base action model."""
    action_type: ActionType
    description: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    requires_approval: bool = False


class ActionCreate(ActionBase):
    """Model for creating actions."""
    ticket_id: int


class ActionResult(BaseModel):
    """Model for action results."""
    success: bool
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: Optional[int] = None


class Action(ActionBase):
    """Complete action model."""
    id: int
    ticket_id: int
    success: bool
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: Optional[int] = None
    approved: Optional[bool] = None
    approved_by: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class KnowledgeDocumentBase(BaseModel):
    """Base knowledge document model."""
    title: str
    content: str
    source: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    """Model for creating knowledge documents."""
    pass


class KnowledgeDocument(KnowledgeDocumentBase):
    """Complete knowledge document model."""
    id: int
    embedding_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class SearchQuery(BaseModel):
    """Model for search queries."""
    query: str
    limit: int = Field(default=10, ge=1, le=100)
    threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    category: Optional[str] = None


class SearchResult(BaseModel):
    """Model for search results."""
    document: KnowledgeDocument
    score: float
    relevance: str  # high, medium, low
