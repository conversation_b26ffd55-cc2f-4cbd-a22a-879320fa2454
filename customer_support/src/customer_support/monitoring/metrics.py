"""Metrics collection and monitoring."""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
import structlog
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry

logger = structlog.get_logger(__name__)


class MetricsCollector:
    """Collects and manages application metrics."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize metrics collector."""
        self.config = config
        self.logger = logger.bind(component="metrics_collector")
        
        # Create custom registry
        self.registry = CollectorRegistry()
        
        # Initialize Prometheus metrics
        self._init_prometheus_metrics()
        
        # In-memory metrics for real-time monitoring
        self.ticket_metrics = defaultdict(int)
        self.agent_metrics = defaultdict(int)
        self.performance_metrics = defaultdict(list)
        self.error_metrics = defaultdict(int)
        
        # Time-series data (last 24 hours)
        self.time_series_data = defaultdict(lambda: deque(maxlen=1440))  # 1 minute intervals
        
        # Configuration
        self.enable_prometheus = config.get("enable_prometheus", True)
        self.retention_hours = config.get("retention_hours", 24)
    
    def _init_prometheus_metrics(self) -> None:
        """Initialize Prometheus metrics."""
        if not self.enable_prometheus:
            return
        
        # Ticket metrics
        self.tickets_processed = Counter(
            'tickets_processed_total',
            'Total number of tickets processed',
            ['status', 'source', 'priority'],
            registry=self.registry
        )
        
        self.ticket_processing_time = Histogram(
            'ticket_processing_seconds',
            'Time spent processing tickets',
            ['status', 'complexity'],
            registry=self.registry
        )
        
        # Agent metrics
        self.agent_actions = Counter(
            'agent_actions_total',
            'Total number of agent actions',
            ['action_type', 'success'],
            registry=self.registry
        )
        
        self.agent_response_time = Histogram(
            'agent_response_seconds',
            'Agent response generation time',
            ['model', 'complexity'],
            registry=self.registry
        )
        
        # Knowledge base metrics
        self.knowledge_searches = Counter(
            'knowledge_searches_total',
            'Total knowledge base searches',
            ['category', 'success'],
            registry=self.registry
        )
        
        self.knowledge_search_time = Histogram(
            'knowledge_search_seconds',
            'Knowledge base search time',
            registry=self.registry
        )
        
        # System metrics
        self.active_tickets = Gauge(
            'active_tickets',
            'Number of currently active tickets',
            registry=self.registry
        )
        
        self.agent_utilization = Gauge(
            'agent_utilization_percent',
            'Agent utilization percentage',
            registry=self.registry
        )
        
        # Error metrics
        self.errors_total = Counter(
            'errors_total',
            'Total number of errors',
            ['component', 'error_type'],
            registry=self.registry
        )
    
    def record_ticket_processed(self, ticket_data: Dict[str, Any], 
                              processing_time: float, success: bool) -> None:
        """Record ticket processing metrics."""
        try:
            status = "success" if success else "failed"
            source = ticket_data.get("source", "unknown")
            priority = ticket_data.get("priority", "medium")
            complexity = ticket_data.get("complexity", "medium")
            
            # Update counters
            self.ticket_metrics[f"processed_{status}"] += 1
            self.ticket_metrics[f"source_{source}"] += 1
            self.ticket_metrics[f"priority_{priority}"] += 1
            
            # Record processing time
            self.performance_metrics["ticket_processing_time"].append(processing_time)
            
            # Prometheus metrics
            if self.enable_prometheus:
                self.tickets_processed.labels(
                    status=status, 
                    source=source, 
                    priority=priority
                ).inc()
                
                self.ticket_processing_time.labels(
                    status=status,
                    complexity=complexity
                ).observe(processing_time)
            
            # Time series data
            current_minute = int(time.time() // 60)
            self.time_series_data["tickets_processed"].append({
                "timestamp": current_minute,
                "value": 1,
                "status": status
            })
            
            self.logger.info("Recorded ticket processing metrics", 
                           ticket_id=ticket_data.get("id"),
                           processing_time=processing_time,
                           success=success)
            
        except Exception as e:
            self.logger.error("Failed to record ticket metrics", error=str(e))
    
    def record_agent_action(self, action_type: str, execution_time: float, 
                          success: bool, context: Dict[str, Any]) -> None:
        """Record agent action metrics."""
        try:
            status = "success" if success else "failed"
            
            # Update counters
            self.agent_metrics[f"action_{action_type}"] += 1
            self.agent_metrics[f"action_{status}"] += 1
            
            # Record execution time
            self.performance_metrics[f"action_{action_type}_time"].append(execution_time)
            
            # Prometheus metrics
            if self.enable_prometheus:
                self.agent_actions.labels(
                    action_type=action_type,
                    success=str(success)
                ).inc()
            
            # Time series data
            current_minute = int(time.time() // 60)
            self.time_series_data["agent_actions"].append({
                "timestamp": current_minute,
                "action_type": action_type,
                "execution_time": execution_time,
                "success": success
            })
            
        except Exception as e:
            self.logger.error("Failed to record agent action metrics", error=str(e))
    
    def record_llm_interaction(self, model: str, prompt_length: int, 
                             response_length: int, response_time: float,
                             success: bool) -> None:
        """Record LLM interaction metrics."""
        try:
            # Update counters
            self.agent_metrics[f"llm_{model}_calls"] += 1
            self.agent_metrics["llm_total_calls"] += 1
            
            if success:
                self.agent_metrics["llm_successful_calls"] += 1
            else:
                self.agent_metrics["llm_failed_calls"] += 1
            
            # Record performance metrics
            self.performance_metrics["llm_response_time"].append(response_time)
            self.performance_metrics["llm_prompt_length"].append(prompt_length)
            self.performance_metrics["llm_response_length"].append(response_length)
            
            # Prometheus metrics
            if self.enable_prometheus:
                complexity = "simple" if prompt_length < 1000 else "complex"
                self.agent_response_time.labels(
                    model=model,
                    complexity=complexity
                ).observe(response_time)
            
        except Exception as e:
            self.logger.error("Failed to record LLM metrics", error=str(e))
    
    def record_knowledge_search(self, query: str, results_count: int, 
                              search_time: float, category: Optional[str] = None) -> None:
        """Record knowledge base search metrics."""
        try:
            success = results_count > 0
            category = category or "general"
            
            # Update counters
            self.agent_metrics["knowledge_searches"] += 1
            self.agent_metrics[f"knowledge_category_{category}"] += 1
            
            if success:
                self.agent_metrics["knowledge_successful_searches"] += 1
            else:
                self.agent_metrics["knowledge_empty_searches"] += 1
            
            # Record performance metrics
            self.performance_metrics["knowledge_search_time"].append(search_time)
            self.performance_metrics["knowledge_results_count"].append(results_count)
            
            # Prometheus metrics
            if self.enable_prometheus:
                self.knowledge_searches.labels(
                    category=category,
                    success=str(success)
                ).inc()
                
                self.knowledge_search_time.observe(search_time)
            
        except Exception as e:
            self.logger.error("Failed to record knowledge search metrics", error=str(e))
    
    def record_error(self, component: str, error_type: str, error_message: str) -> None:
        """Record error metrics."""
        try:
            # Update error counters
            self.error_metrics[f"{component}_{error_type}"] += 1
            self.error_metrics["total_errors"] += 1
            
            # Prometheus metrics
            if self.enable_prometheus:
                self.errors_total.labels(
                    component=component,
                    error_type=error_type
                ).inc()
            
            # Time series data
            current_minute = int(time.time() // 60)
            self.time_series_data["errors"].append({
                "timestamp": current_minute,
                "component": component,
                "error_type": error_type,
                "message": error_message[:100]  # Truncate long messages
            })
            
        except Exception as e:
            self.logger.error("Failed to record error metrics", error=str(e))
    
    def update_system_metrics(self, active_tickets: int, agent_utilization: float) -> None:
        """Update system-level metrics."""
        try:
            # Update gauges
            if self.enable_prometheus:
                self.active_tickets.set(active_tickets)
                self.agent_utilization.set(agent_utilization)
            
            # Time series data
            current_minute = int(time.time() // 60)
            self.time_series_data["system_metrics"].append({
                "timestamp": current_minute,
                "active_tickets": active_tickets,
                "agent_utilization": agent_utilization
            })
            
        except Exception as e:
            self.logger.error("Failed to update system metrics", error=str(e))
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        try:
            # Calculate averages for performance metrics
            performance_summary = {}
            for metric_name, values in self.performance_metrics.items():
                if values:
                    performance_summary[metric_name] = {
                        "avg": sum(values) / len(values),
                        "min": min(values),
                        "max": max(values),
                        "count": len(values)
                    }
            
            return {
                "ticket_metrics": dict(self.ticket_metrics),
                "agent_metrics": dict(self.agent_metrics),
                "error_metrics": dict(self.error_metrics),
                "performance_metrics": performance_summary,
                "collection_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error("Failed to get metrics summary", error=str(e))
            return {}
    
    def get_time_series_data(self, metric_name: str, 
                           hours: int = 1) -> List[Dict[str, Any]]:
        """Get time series data for a specific metric."""
        try:
            if metric_name not in self.time_series_data:
                return []
            
            # Calculate cutoff time
            cutoff_time = int(time.time() // 60) - (hours * 60)
            
            # Filter data
            filtered_data = [
                data for data in self.time_series_data[metric_name]
                if data["timestamp"] >= cutoff_time
            ]
            
            return filtered_data
            
        except Exception as e:
            self.logger.error("Failed to get time series data", 
                            metric_name=metric_name, error=str(e))
            return []
    
    def cleanup_old_data(self) -> None:
        """Clean up old time series data."""
        try:
            cutoff_time = int(time.time() // 60) - (self.retention_hours * 60)
            
            for metric_name, data_queue in self.time_series_data.items():
                # Remove old data points
                while data_queue and data_queue[0]["timestamp"] < cutoff_time:
                    data_queue.popleft()
            
            # Clean up performance metrics (keep last 1000 entries)
            for metric_name, values in self.performance_metrics.items():
                if len(values) > 1000:
                    self.performance_metrics[metric_name] = values[-1000:]
            
        except Exception as e:
            self.logger.error("Failed to cleanup old data", error=str(e))
    
    def get_registry(self) -> CollectorRegistry:
        """Get the Prometheus registry."""
        return self.registry
