"""Knowledge base management and retrieval system."""

import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
import structlog
from sqlalchemy.orm import Session

from .embeddings import EmbeddingManager
from ..core.models import (
    KnowledgeDocument, 
    KnowledgeDocumentCreate, 
    KnowledgeDocumentModel,
    SearchQuery, 
    SearchResult
)

logger = structlog.get_logger(__name__)


class KnowledgeBaseError(Exception):
    """Base exception for knowledge base operations."""
    pass


class KnowledgeBase:
    """Manages the knowledge base with vector search capabilities."""
    
    def __init__(self, db_session: Session, embedding_manager: EmbeddingManager, config: Dict[str, Any]):
        """Initialize knowledge base."""
        self.db = db_session
        self.embedding_manager = embedding_manager
        self.config = config
        self.logger = logger.bind(component="knowledge_base")
        
        # Configuration
        self.max_documents = config.get("max_documents", 10000)
        self.chunk_size = config.get("chunk_size", 1000)
        self.chunk_overlap = config.get("chunk_overlap", 200)
        self.auto_update = config.get("auto_update", True)
        self.update_interval = config.get("update_interval", 3600)  # seconds
    
    async def initialize(self) -> None:
        """Initialize the knowledge base."""
        try:
            # Initialize embedding manager
            await self.embedding_manager.initialize()
            
            # Sync database with vector store
            await self._sync_database_with_vectors()
            
            self.logger.info("Knowledge base initialized")
            
        except Exception as e:
            self.logger.error("Failed to initialize knowledge base", error=str(e))
            raise KnowledgeBaseError(f"Initialization failed: {e}")
    
    async def add_document(self, document_data: KnowledgeDocumentCreate) -> KnowledgeDocument:
        """Add a new document to the knowledge base."""
        try:
            # Check document limit
            current_count = self.db.query(KnowledgeDocumentModel).count()
            if current_count >= self.max_documents:
                raise KnowledgeBaseError(f"Maximum document limit ({self.max_documents}) reached")
            
            # Create database record
            db_document = KnowledgeDocumentModel(
                title=document_data.title,
                content=document_data.content,
                source=document_data.source,
                category=document_data.category,
                tags=document_data.tags,
                metadata=document_data.metadata
            )
            
            self.db.add(db_document)
            self.db.commit()
            self.db.refresh(db_document)
            
            # Convert to Pydantic model
            document = KnowledgeDocument.from_orm(db_document)
            
            # Add to vector database
            embedding_id = await self.embedding_manager.add_document(document)
            
            # Update database with embedding ID
            db_document.embedding_id = embedding_id
            self.db.commit()
            
            # Update Pydantic model
            document.embedding_id = embedding_id
            
            self.logger.info("Added document to knowledge base", 
                           document_id=document.id,
                           title=document.title)
            
            return document
            
        except Exception as e:
            self.logger.error("Failed to add document", error=str(e))
            self.db.rollback()
            raise KnowledgeBaseError(f"Failed to add document: {e}")
    
    async def update_document(self, document_id: int, update_data: Dict[str, Any]) -> Optional[KnowledgeDocument]:
        """Update an existing document."""
        try:
            # Get existing document
            db_document = self.db.query(KnowledgeDocumentModel).filter(
                KnowledgeDocumentModel.id == document_id
            ).first()
            
            if not db_document:
                return None
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(db_document, field):
                    setattr(db_document, field, value)
            
            db_document.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(db_document)
            
            # Convert to Pydantic model
            document = KnowledgeDocument.from_orm(db_document)
            
            # Update vector database if content changed
            if "content" in update_data or "title" in update_data:
                if db_document.embedding_id:
                    # Remove old embedding
                    await self.embedding_manager.remove_document(db_document.embedding_id)
                
                # Add new embedding
                embedding_id = await self.embedding_manager.add_document(document)
                
                # Update database
                db_document.embedding_id = embedding_id
                self.db.commit()
                
                document.embedding_id = embedding_id
            
            self.logger.info("Updated document", document_id=document_id)
            return document
            
        except Exception as e:
            self.logger.error("Failed to update document", document_id=document_id, error=str(e))
            self.db.rollback()
            raise KnowledgeBaseError(f"Failed to update document: {e}")
    
    async def delete_document(self, document_id: int) -> bool:
        """Delete a document from the knowledge base."""
        try:
            # Get document
            db_document = self.db.query(KnowledgeDocumentModel).filter(
                KnowledgeDocumentModel.id == document_id
            ).first()
            
            if not db_document:
                return False
            
            # Remove from vector database
            if db_document.embedding_id:
                await self.embedding_manager.remove_document(db_document.embedding_id)
            
            # Remove from database
            self.db.delete(db_document)
            self.db.commit()
            
            self.logger.info("Deleted document", document_id=document_id)
            return True
            
        except Exception as e:
            self.logger.error("Failed to delete document", document_id=document_id, error=str(e))
            self.db.rollback()
            return False
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search the knowledge base."""
        try:
            # Perform vector search
            results = await self.embedding_manager.search(query)
            
            self.logger.info("Performed knowledge base search", 
                           query=query.query,
                           results_count=len(results))
            
            return results
            
        except Exception as e:
            self.logger.error("Search failed", query=query.query, error=str(e))
            raise KnowledgeBaseError(f"Search failed: {e}")
    
    async def get_document(self, document_id: int) -> Optional[KnowledgeDocument]:
        """Get a specific document by ID."""
        try:
            db_document = self.db.query(KnowledgeDocumentModel).filter(
                KnowledgeDocumentModel.id == document_id
            ).first()
            
            if db_document:
                return KnowledgeDocument.from_orm(db_document)
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get document", document_id=document_id, error=str(e))
            return None
    
    async def list_documents(self, category: Optional[str] = None, 
                           limit: int = 100, offset: int = 0) -> List[KnowledgeDocument]:
        """List documents with optional filtering."""
        try:
            query = self.db.query(KnowledgeDocumentModel)
            
            if category:
                query = query.filter(KnowledgeDocumentModel.category == category)
            
            db_documents = query.offset(offset).limit(limit).all()
            
            return [KnowledgeDocument.from_orm(doc) for doc in db_documents]
            
        except Exception as e:
            self.logger.error("Failed to list documents", error=str(e))
            return []
    
    async def get_categories(self) -> List[str]:
        """Get all available categories."""
        try:
            categories = self.db.query(KnowledgeDocumentModel.category).distinct().all()
            return [cat[0] for cat in categories if cat[0]]
            
        except Exception as e:
            self.logger.error("Failed to get categories", error=str(e))
            return []
    
    async def bulk_import(self, documents: List[KnowledgeDocumentCreate]) -> Dict[str, Any]:
        """Import multiple documents in bulk."""
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for doc_data in documents:
            try:
                await self.add_document(doc_data)
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append({
                    "title": doc_data.title,
                    "error": str(e)
                })
        
        self.logger.info("Bulk import completed", 
                        success=results["success"],
                        failed=results["failed"])
        
        return results
    
    async def _sync_database_with_vectors(self) -> None:
        """Sync database documents with vector store."""
        try:
            # Get all documents from database
            db_documents = self.db.query(KnowledgeDocumentModel).all()
            
            # Check which documents need vector embeddings
            missing_embeddings = []
            for db_doc in db_documents:
                if not db_doc.embedding_id:
                    missing_embeddings.append(db_doc)
            
            if missing_embeddings:
                self.logger.info("Adding missing embeddings", count=len(missing_embeddings))
                
                for db_doc in missing_embeddings:
                    try:
                        document = KnowledgeDocument.from_orm(db_doc)
                        embedding_id = await self.embedding_manager.add_document(document)
                        
                        db_doc.embedding_id = embedding_id
                        self.db.commit()
                        
                    except Exception as e:
                        self.logger.error("Failed to add embedding for document", 
                                        document_id=db_doc.id, error=str(e))
            
            # Save vector index
            await self.embedding_manager.save_index()
            
        except Exception as e:
            self.logger.error("Failed to sync database with vectors", error=str(e))
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics."""
        try:
            # Database stats
            total_docs = self.db.query(KnowledgeDocumentModel).count()
            categories = await self.get_categories()
            
            # Vector stats
            vector_stats = await self.embedding_manager.get_stats()
            
            return {
                "total_documents": total_docs,
                "categories": len(categories),
                "category_list": categories,
                "vector_stats": vector_stats,
                "max_documents": self.max_documents,
                "chunk_size": self.chunk_size
            }
            
        except Exception as e:
            self.logger.error("Failed to get stats", error=str(e))
            return {}
    
    async def rebuild_index(self) -> bool:
        """Rebuild the entire vector index."""
        try:
            self.logger.info("Starting index rebuild")
            
            # Get all documents
            db_documents = self.db.query(KnowledgeDocumentModel).all()
            
            # Clear existing embeddings
            for db_doc in db_documents:
                if db_doc.embedding_id:
                    await self.embedding_manager.remove_document(db_doc.embedding_id)
                    db_doc.embedding_id = None
            
            self.db.commit()
            
            # Re-add all documents
            for db_doc in db_documents:
                try:
                    document = KnowledgeDocument.from_orm(db_doc)
                    embedding_id = await self.embedding_manager.add_document(document)
                    
                    db_doc.embedding_id = embedding_id
                    self.db.commit()
                    
                except Exception as e:
                    self.logger.error("Failed to re-add document during rebuild", 
                                    document_id=db_doc.id, error=str(e))
            
            # Save index
            await self.embedding_manager.save_index()
            
            self.logger.info("Index rebuild completed")
            return True
            
        except Exception as e:
            self.logger.error("Index rebuild failed", error=str(e))
            return False
