"""Embeddings and vector operations for knowledge base."""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import structlog
from sentence_transformers import SentenceTransformer
import faiss
import pickle
import os
from datetime import datetime

from ..core.models import KnowledgeDocument, SearchResult, SearchQuery

logger = structlog.get_logger(__name__)


class EmbeddingError(Exception):
    """Base exception for embedding operations."""
    pass


class EmbeddingManager:
    """Manages embeddings and vector operations."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize embedding manager."""
        self.config = config
        self.logger = logger.bind(component="embedding_manager")
        
        # Model configuration
        self.model_name = config.get("model_name", "all-MiniLM-L6-v2")
        self.vector_db_path = config.get("vector_db_path", "./data/vector_db")
        self.dimension = config.get("dimension", 384)  # Default for all-MiniLM-L6-v2
        
        # Initialize components
        self.model: Optional[SentenceTransformer] = None
        self.index: Optional[faiss.IndexFlatIP] = None  # Inner product for cosine similarity
        self.document_store: Dict[int, KnowledgeDocument] = {}
        self.id_mapping: Dict[str, int] = {}  # embedding_id -> faiss_id
        self.next_id = 0
        
        # Ensure vector DB directory exists
        os.makedirs(self.vector_db_path, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize the embedding model and vector database."""
        try:
            # Load sentence transformer model
            self.logger.info("Loading embedding model", model_name=self.model_name)
            self.model = SentenceTransformer(self.model_name)
            
            # Update dimension based on model
            self.dimension = self.model.get_sentence_embedding_dimension()
            
            # Initialize or load FAISS index
            await self._load_or_create_index()
            
            self.logger.info("Embedding manager initialized", 
                           model_name=self.model_name,
                           dimension=self.dimension,
                           documents_count=len(self.document_store))
            
        except Exception as e:
            self.logger.error("Failed to initialize embedding manager", error=str(e))
            raise EmbeddingError(f"Initialization failed: {e}")
    
    async def _load_or_create_index(self) -> None:
        """Load existing index or create new one."""
        index_path = os.path.join(self.vector_db_path, "faiss_index.bin")
        metadata_path = os.path.join(self.vector_db_path, "metadata.pkl")
        
        if os.path.exists(index_path) and os.path.exists(metadata_path):
            # Load existing index
            self.logger.info("Loading existing FAISS index")
            self.index = faiss.read_index(index_path)
            
            with open(metadata_path, "rb") as f:
                metadata = pickle.load(f)
                self.document_store = metadata["document_store"]
                self.id_mapping = metadata["id_mapping"]
                self.next_id = metadata["next_id"]
        else:
            # Create new index
            self.logger.info("Creating new FAISS index")
            self.index = faiss.IndexFlatIP(self.dimension)
            self.document_store = {}
            self.id_mapping = {}
            self.next_id = 0
    
    async def save_index(self) -> None:
        """Save the FAISS index and metadata to disk."""
        try:
            index_path = os.path.join(self.vector_db_path, "faiss_index.bin")
            metadata_path = os.path.join(self.vector_db_path, "metadata.pkl")
            
            # Save FAISS index
            faiss.write_index(self.index, index_path)
            
            # Save metadata
            metadata = {
                "document_store": self.document_store,
                "id_mapping": self.id_mapping,
                "next_id": self.next_id,
                "saved_at": datetime.utcnow().isoformat()
            }
            
            with open(metadata_path, "wb") as f:
                pickle.dump(metadata, f)
            
            self.logger.info("Saved FAISS index and metadata")
            
        except Exception as e:
            self.logger.error("Failed to save index", error=str(e))
            raise EmbeddingError(f"Save failed: {e}")
    
    async def add_document(self, document: KnowledgeDocument) -> str:
        """Add a document to the vector database."""
        if not self.model or not self.index:
            raise EmbeddingError("Embedding manager not initialized")
        
        try:
            # Create text for embedding
            text_to_embed = f"{document.title}\n\n{document.content}"
            
            # Generate embedding
            embedding = self.model.encode([text_to_embed])
            
            # Normalize for cosine similarity
            embedding = embedding / np.linalg.norm(embedding, axis=1, keepdims=True)
            
            # Add to FAISS index
            faiss_id = self.next_id
            self.index.add(embedding.astype(np.float32))
            
            # Store document and mapping
            self.document_store[faiss_id] = document
            embedding_id = f"emb_{faiss_id}_{int(datetime.utcnow().timestamp())}"
            self.id_mapping[embedding_id] = faiss_id
            self.next_id += 1
            
            self.logger.info("Added document to vector database", 
                           document_id=document.id,
                           embedding_id=embedding_id,
                           faiss_id=faiss_id)
            
            return embedding_id
            
        except Exception as e:
            self.logger.error("Failed to add document", document_id=document.id, error=str(e))
            raise EmbeddingError(f"Failed to add document: {e}")
    
    async def remove_document(self, embedding_id: str) -> bool:
        """Remove a document from the vector database."""
        try:
            if embedding_id not in self.id_mapping:
                self.logger.warning("Document not found for removal", embedding_id=embedding_id)
                return False
            
            faiss_id = self.id_mapping[embedding_id]
            
            # Remove from stores
            del self.document_store[faiss_id]
            del self.id_mapping[embedding_id]
            
            # Note: FAISS doesn't support efficient removal, so we rebuild the index
            await self._rebuild_index()
            
            self.logger.info("Removed document from vector database", embedding_id=embedding_id)
            return True
            
        except Exception as e:
            self.logger.error("Failed to remove document", embedding_id=embedding_id, error=str(e))
            return False
    
    async def _rebuild_index(self) -> None:
        """Rebuild the FAISS index after document removal."""
        if not self.model:
            return
        
        # Create new index
        new_index = faiss.IndexFlatIP(self.dimension)
        new_document_store = {}
        new_id_mapping = {}
        new_id = 0
        
        # Re-add all documents
        for old_faiss_id, document in self.document_store.items():
            text_to_embed = f"{document.title}\n\n{document.content}"
            embedding = self.model.encode([text_to_embed])
            embedding = embedding / np.linalg.norm(embedding, axis=1, keepdims=True)
            
            new_index.add(embedding.astype(np.float32))
            new_document_store[new_id] = document
            
            # Update mapping
            old_embedding_id = None
            for emb_id, fid in self.id_mapping.items():
                if fid == old_faiss_id:
                    old_embedding_id = emb_id
                    break
            
            if old_embedding_id:
                new_id_mapping[old_embedding_id] = new_id
            
            new_id += 1
        
        # Replace old index
        self.index = new_index
        self.document_store = new_document_store
        self.id_mapping = new_id_mapping
        self.next_id = new_id
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search for similar documents."""
        if not self.model or not self.index:
            raise EmbeddingError("Embedding manager not initialized")
        
        try:
            # Generate query embedding
            query_embedding = self.model.encode([query.query])
            query_embedding = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
            
            # Search in FAISS
            scores, indices = self.index.search(query_embedding.astype(np.float32), query.limit)
            
            # Convert results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:  # No more results
                    break
                
                if score < query.threshold:
                    continue
                
                document = self.document_store.get(idx)
                if document:
                    # Filter by category if specified
                    if query.category and document.category != query.category:
                        continue
                    
                    # Determine relevance level
                    if score >= 0.9:
                        relevance = "high"
                    elif score >= 0.7:
                        relevance = "medium"
                    else:
                        relevance = "low"
                    
                    results.append(SearchResult(
                        document=document,
                        score=float(score),
                        relevance=relevance
                    ))
            
            self.logger.info("Performed vector search", 
                           query=query.query,
                           results_count=len(results),
                           threshold=query.threshold)
            
            return results
            
        except Exception as e:
            self.logger.error("Search failed", query=query.query, error=str(e))
            raise EmbeddingError(f"Search failed: {e}")
    
    async def get_similar_documents(self, document_id: int, limit: int = 5) -> List[SearchResult]:
        """Find documents similar to a given document."""
        # Find the document
        target_document = None
        for doc in self.document_store.values():
            if doc.id == document_id:
                target_document = doc
                break
        
        if not target_document:
            return []
        
        # Use the document content as query
        query = SearchQuery(
            query=f"{target_document.title} {target_document.content}",
            limit=limit + 1,  # +1 to exclude the original document
            threshold=0.5
        )
        
        results = await self.search(query)
        
        # Filter out the original document
        filtered_results = [r for r in results if r.document.id != document_id]
        
        return filtered_results[:limit]
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector database."""
        return {
            "total_documents": len(self.document_store),
            "vector_dimension": self.dimension,
            "model_name": self.model_name,
            "index_size": self.index.ntotal if self.index else 0,
            "next_id": self.next_id
        }
