"""Main FastAPI application for customer support agent."""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
import structlog

# Database
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Internal imports
from .config import settings
from .core.models import (
    Base, Ticket, TicketCreate, TicketUpdate, Message, MessageCreate,
    KnowledgeDocument, KnowledgeDocumentCreate, SearchQuery
)
from .ingestion import IngestionManager, EmailTicketIngester, APITicketIngester
from .automation import BrowserAutomator, ZendeskConnector, FreshdeskConnector
from .knowledge import EmbeddingManager, KnowledgeBase
from .agent import GeminiClient, SupportAgent
from .tools import create_default_registry
from .safety import SafetyManager
from .monitoring import MetricsCollector

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Global components
db_engine = None
db_session_factory = None
support_agent = None
ingestion_manager = None
metrics_collector = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    await startup_event()
    yield
    # Shutdown
    await shutdown_event()


# Create FastAPI app
app = FastAPI(
    title="Customer Support Agent",
    description="AI-powered customer support agent with Gemini API and BrowserUse",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def startup_event():
    """Initialize application components."""
    global db_engine, db_session_factory, support_agent, ingestion_manager, metrics_collector
    
    logger.info("Starting customer support agent application")
    
    try:
        # Initialize database
        db_engine = create_engine(settings.database_url)
        Base.metadata.create_all(bind=db_engine)
        db_session_factory = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)
        
        # Initialize metrics collector
        metrics_collector = MetricsCollector({
            "enable_prometheus": settings.enable_telemetry,
            "retention_hours": 24
        })
        
        # Initialize embedding manager
        embedding_manager = EmbeddingManager({
            "model_name": settings.embeddings_model,
            "vector_db_path": settings.vector_db_path
        })
        await embedding_manager.initialize()
        
        # Initialize knowledge base
        db_session = db_session_factory()
        knowledge_base = KnowledgeBase(
            db_session=db_session,
            embedding_manager=embedding_manager,
            config={
                "max_documents": settings.kb_max_documents,
                "chunk_size": settings.kb_chunk_size,
                "chunk_overlap": settings.kb_chunk_overlap
            }
        )
        await knowledge_base.initialize()
        
        # Initialize browser automation
        browser_automator = None
        if settings.browseruse_headless is not None:
            browser_automator = BrowserAutomator({
                "headless": settings.browseruse_headless,
                "timeout": settings.browseruse_timeout
            })
            await browser_automator.start()
        
        # Initialize API connectors
        api_connectors = {}
        if settings.helpdesk_api_url:
            if "zendesk" in settings.helpdesk_api_url.lower():
                api_connectors["zendesk"] = ZendeskConnector({
                    "api_url": settings.helpdesk_api_url,
                    "api_key": settings.helpdesk_api_key,
                    "username": settings.helpdesk_username,
                    "password": settings.helpdesk_password
                })
            elif "freshdesk" in settings.helpdesk_api_url.lower():
                api_connectors["freshdesk"] = FreshdeskConnector({
                    "api_url": settings.helpdesk_api_url,
                    "api_key": settings.helpdesk_api_key,
                    "username": settings.helpdesk_username,
                    "password": settings.helpdesk_password
                })
        
        # Connect API connectors
        for name, connector in api_connectors.items():
            try:
                await connector.connect()
                logger.info("Connected to API connector", connector=name)
            except Exception as e:
                logger.error("Failed to connect to API connector", connector=name, error=str(e))
        
        # Initialize safety manager
        safety_manager = SafetyManager({
            "enable_rate_limiting": True,
            "enable_whitelisting": True,
            "enable_approvals": settings.enable_human_approval,
            "rate_limit_per_minute": settings.rate_limit_per_minute,
            "whitelist": {
                "allowed_actions": [
                    "get_ticket", "search_knowledge", "post_reply", 
                    "update_ticket", "escalate_ticket"
                ],
                "high_risk_actions": ["close_ticket", "escalate_ticket"],
                "restricted_actions": []
            }
        })
        
        # Initialize tool registry
        tool_registry = create_default_registry(
            db_session=db_session,
            knowledge_base=knowledge_base,
            browser_automator=browser_automator,
            api_connectors=api_connectors,
            config={}
        )
        
        # Initialize Gemini client
        gemini_client = GeminiClient({
            "api_key": settings.gemini_api_key,
            "model_name": settings.gemini_model,
            "temperature": 0.7,
            "max_tokens": 2048
        })
        
        # Initialize support agent
        support_agent = SupportAgent(
            gemini_client=gemini_client,
            knowledge_base=knowledge_base,
            tool_registry=tool_registry,
            safety_manager=safety_manager,
            config={
                "max_actions_per_ticket": settings.max_actions_per_ticket,
                "confidence_threshold": 0.7
            }
        )
        
        # Initialize ingestion manager
        ingesters = []
        
        # Email ingester
        if settings.email_username and settings.email_password:
            email_ingester = EmailTicketIngester({
                "imap_server": settings.email_imap_server,
                "imap_port": settings.email_imap_port,
                "username": settings.email_username,
                "password": settings.email_password,
                "use_ssl": settings.email_use_ssl
            })
            ingesters.append(email_ingester)
        
        # API ingester
        if settings.helpdesk_api_url:
            api_ingester = APITicketIngester({
                "api_url": settings.helpdesk_api_url,
                "api_key": settings.helpdesk_api_key,
                "username": settings.helpdesk_username,
                "password": settings.helpdesk_password
            })
            ingesters.append(api_ingester)
        
        if ingesters:
            from .ingestion.base import TicketProcessor
            ticket_processor = TicketProcessor(db_session)
            ingestion_manager = IngestionManager(ingesters, ticket_processor)
            await ingestion_manager.start_ingestion()
        
        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.error("Application startup failed", error=str(e))
        raise


async def shutdown_event():
    """Cleanup application components."""
    global ingestion_manager, support_agent
    
    logger.info("Shutting down customer support agent application")
    
    try:
        if ingestion_manager:
            await ingestion_manager.stop_ingestion()
        
        # Additional cleanup can be added here
        
        logger.info("Application shutdown completed")
        
    except Exception as e:
        logger.error("Error during application shutdown", error=str(e))


def get_db() -> Session:
    """Get database session."""
    db = db_session_factory()
    try:
        yield db
    finally:
        db.close()


# API Routes

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Customer Support Agent API",
        "version": "1.0.0",
        "status": "active"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": str(datetime.utcnow()),
        "components": {
            "database": "healthy",
            "agent": "active" if support_agent else "inactive",
            "ingestion": "active" if ingestion_manager else "inactive"
        }
    }


@app.post("/tickets/ingest")
async def ingest_tickets(background_tasks: BackgroundTasks):
    """Manually trigger ticket ingestion."""
    if not ingestion_manager:
        raise HTTPException(status_code=503, detail="Ingestion manager not available")
    
    try:
        # Run ingestion in background
        background_tasks.add_task(ingestion_manager.ingest_batch, 10)
        return {"message": "Ticket ingestion started", "status": "processing"}
    except Exception as e:
        logger.error("Failed to start ticket ingestion", error=str(e))
        raise HTTPException(status_code=500, detail=f"Ingestion failed: {e}")


@app.get("/tickets/{ticket_id}")
async def get_ticket(ticket_id: int, db: Session = Depends(get_db)):
    """Get ticket details."""
    try:
        from .core.models import TicketModel
        db_ticket = db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
        
        if not db_ticket:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        return Ticket.from_orm(db_ticket)
    except Exception as e:
        logger.error("Failed to get ticket", ticket_id=ticket_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get ticket: {e}")


@app.post("/tickets/{ticket_id}/process")
async def process_ticket(ticket_id: int, db: Session = Depends(get_db)):
    """Process a ticket with the AI agent."""
    if not support_agent:
        raise HTTPException(status_code=503, detail="Support agent not available")
    
    try:
        # Get ticket
        from .core.models import TicketModel, MessageModel
        db_ticket = db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
        
        if not db_ticket:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        ticket = Ticket.from_orm(db_ticket)
        
        # Get conversation history
        db_messages = db.query(MessageModel).filter(
            MessageModel.ticket_id == ticket_id
        ).order_by(MessageModel.created_at).all()
        
        messages = [Message.from_orm(msg) for msg in db_messages]
        
        # Process ticket
        result = await support_agent.process_ticket(ticket, messages)
        
        # Record metrics
        if metrics_collector:
            metrics_collector.record_ticket_processed(
                ticket.dict(),
                processing_time=1.0,  # Would be calculated from actual processing time
                success=True
            )
        
        return result
        
    except Exception as e:
        logger.error("Failed to process ticket", ticket_id=ticket_id, error=str(e))
        if metrics_collector:
            metrics_collector.record_error("agent", "processing_error", str(e))
        raise HTTPException(status_code=500, detail=f"Processing failed: {e}")


@app.get("/knowledge/search")
async def search_knowledge(q: str, limit: int = 5, threshold: float = 0.7):
    """Search the knowledge base."""
    if not support_agent:
        raise HTTPException(status_code=503, detail="Support agent not available")
    
    try:
        search_query = SearchQuery(query=q, limit=limit, threshold=threshold)
        results = await support_agent.knowledge_base.search(search_query)
        
        # Record metrics
        if metrics_collector:
            metrics_collector.record_knowledge_search(
                query=q,
                results_count=len(results),
                search_time=0.1  # Would be calculated from actual search time
            )
        
        return {"query": q, "results": [result.dict() for result in results]}
        
    except Exception as e:
        logger.error("Knowledge search failed", query=q, error=str(e))
        raise HTTPException(status_code=500, detail=f"Search failed: {e}")


@app.get("/metrics")
async def get_metrics():
    """Get Prometheus metrics."""
    if not metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not available")
    
    try:
        registry = metrics_collector.get_registry()
        return Response(generate_latest(registry), media_type=CONTENT_TYPE_LATEST)
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Metrics unavailable: {e}")


@app.get("/stats")
async def get_stats():
    """Get application statistics."""
    try:
        stats = {}
        
        if metrics_collector:
            stats["metrics"] = metrics_collector.get_metrics_summary()
        
        if support_agent:
            stats["agent"] = await support_agent.get_agent_stats()
        
        return stats
        
    except Exception as e:
        logger.error("Failed to get stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Stats unavailable: {e}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        workers=1  # Use 1 worker for development
    )
