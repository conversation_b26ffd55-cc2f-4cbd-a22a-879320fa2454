"""Safety guardrails and approval mechanisms."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
import structlog
from enum import Enum

logger = structlog.get_logger(__name__)


class ApprovalStatus(str, Enum):
    """Approval status enumeration."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


class SafetyError(Exception):
    """Base exception for safety-related errors."""
    pass


class RateLimiter:
    """Rate limiter for controlling action frequency."""
    
    def __init__(self, max_requests: int, time_window: int):
        """Initialize rate limiter.
        
        Args:
            max_requests: Maximum number of requests allowed
            time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, List[datetime]] = {}
    
    def is_allowed(self, identifier: str) -> bool:
        """Check if a request is allowed for the given identifier."""
        now = datetime.utcnow()
        
        # Initialize if not exists
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # Clean old requests
        cutoff_time = now - timedelta(seconds=self.time_window)
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier] 
            if req_time > cutoff_time
        ]
        
        # Check if under limit
        if len(self.requests[identifier]) < self.max_requests:
            self.requests[identifier].append(now)
            return True
        
        return False
    
    def get_remaining_requests(self, identifier: str) -> int:
        """Get remaining requests for identifier."""
        if identifier not in self.requests:
            return self.max_requests
        
        now = datetime.utcnow()
        cutoff_time = now - timedelta(seconds=self.time_window)
        
        # Count valid requests
        valid_requests = [
            req_time for req_time in self.requests[identifier] 
            if req_time > cutoff_time
        ]
        
        return max(0, self.max_requests - len(valid_requests))


class ActionWhitelist:
    """Manages whitelisted actions and restrictions."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize action whitelist."""
        self.config = config
        self.logger = logger.bind(component="action_whitelist")
        
        # Load whitelists
        self.allowed_actions: Set[str] = set(config.get("allowed_actions", []))
        self.restricted_actions: Set[str] = set(config.get("restricted_actions", []))
        self.high_risk_actions: Set[str] = set(config.get("high_risk_actions", []))
        
        # Context-based restrictions
        self.customer_tier_restrictions = config.get("customer_tier_restrictions", {})
        self.time_based_restrictions = config.get("time_based_restrictions", {})
    
    def is_action_allowed(self, action: str, context: Dict[str, Any]) -> bool:
        """Check if an action is allowed in the given context."""
        try:
            # Check if action is explicitly restricted
            if action in self.restricted_actions:
                self.logger.warning("Action is restricted", action=action)
                return False
            
            # Check if action is in whitelist (if whitelist is used)
            if self.allowed_actions and action not in self.allowed_actions:
                self.logger.warning("Action not in whitelist", action=action)
                return False
            
            # Check customer tier restrictions
            customer_tier = context.get("customer_tier", "standard")
            tier_restrictions = self.customer_tier_restrictions.get(customer_tier, [])
            if action in tier_restrictions:
                self.logger.warning("Action restricted for customer tier", 
                                  action=action, 
                                  customer_tier=customer_tier)
                return False
            
            # Check time-based restrictions
            current_hour = datetime.utcnow().hour
            time_restrictions = self.time_based_restrictions.get(action, {})
            allowed_hours = time_restrictions.get("allowed_hours", [])
            if allowed_hours and current_hour not in allowed_hours:
                self.logger.warning("Action restricted at current time", 
                                  action=action, 
                                  current_hour=current_hour)
                return False
            
            return True
            
        except Exception as e:
            self.logger.error("Error checking action allowance", action=action, error=str(e))
            return False
    
    def requires_approval(self, action: str, context: Dict[str, Any]) -> bool:
        """Check if an action requires approval."""
        # High-risk actions always require approval
        if action in self.high_risk_actions:
            return True
        
        # Check context-based approval requirements
        ticket_priority = context.get("ticket_priority", "medium")
        if ticket_priority in ["high", "urgent", "critical"]:
            return True
        
        customer_tier = context.get("customer_tier", "standard")
        if customer_tier in ["premium", "enterprise"]:
            return True
        
        return False


class ApprovalManager:
    """Manages approval requests for risky actions."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize approval manager."""
        self.config = config
        self.logger = logger.bind(component="approval_manager")
        
        # Approval storage
        self.pending_approvals: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.approval_timeout = config.get("approval_timeout", 3600)  # 1 hour
        self.auto_approve_low_risk = config.get("auto_approve_low_risk", False)
        self.approval_channels = config.get("approval_channels", ["email", "slack"])
    
    async def request_approval(self, action: str, context: Dict[str, Any], 
                             requester: str = "ai_agent") -> str:
        """Request approval for an action."""
        try:
            # Generate approval ID
            approval_id = f"approval_{int(datetime.utcnow().timestamp())}_{action}"
            
            # Create approval request
            approval_request = {
                "id": approval_id,
                "action": action,
                "context": context,
                "requester": requester,
                "status": ApprovalStatus.PENDING,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(seconds=self.approval_timeout),
                "approver": None,
                "approved_at": None,
                "rejection_reason": None
            }
            
            # Store approval request
            self.pending_approvals[approval_id] = approval_request
            
            # Send approval notifications
            await self._send_approval_notifications(approval_request)
            
            self.logger.info("Approval requested", 
                           approval_id=approval_id,
                           action=action,
                           requester=requester)
            
            return approval_id
            
        except Exception as e:
            self.logger.error("Failed to request approval", action=action, error=str(e))
            raise SafetyError(f"Approval request failed: {e}")
    
    async def check_approval_status(self, approval_id: str) -> ApprovalStatus:
        """Check the status of an approval request."""
        if approval_id not in self.pending_approvals:
            return ApprovalStatus.REJECTED
        
        approval = self.pending_approvals[approval_id]
        
        # Check if expired
        if datetime.utcnow() > approval["expires_at"]:
            approval["status"] = ApprovalStatus.EXPIRED
            return ApprovalStatus.EXPIRED
        
        return ApprovalStatus(approval["status"])
    
    async def approve_request(self, approval_id: str, approver: str, 
                            reason: Optional[str] = None) -> bool:
        """Approve a pending request."""
        try:
            if approval_id not in self.pending_approvals:
                return False
            
            approval = self.pending_approvals[approval_id]
            
            # Check if still pending
            if approval["status"] != ApprovalStatus.PENDING:
                return False
            
            # Check if not expired
            if datetime.utcnow() > approval["expires_at"]:
                approval["status"] = ApprovalStatus.EXPIRED
                return False
            
            # Approve the request
            approval["status"] = ApprovalStatus.APPROVED
            approval["approver"] = approver
            approval["approved_at"] = datetime.utcnow()
            approval["approval_reason"] = reason
            
            self.logger.info("Request approved", 
                           approval_id=approval_id,
                           approver=approver,
                           action=approval["action"])
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to approve request", approval_id=approval_id, error=str(e))
            return False
    
    async def reject_request(self, approval_id: str, approver: str, 
                           reason: str) -> bool:
        """Reject a pending request."""
        try:
            if approval_id not in self.pending_approvals:
                return False
            
            approval = self.pending_approvals[approval_id]
            
            # Check if still pending
            if approval["status"] != ApprovalStatus.PENDING:
                return False
            
            # Reject the request
            approval["status"] = ApprovalStatus.REJECTED
            approval["approver"] = approver
            approval["approved_at"] = datetime.utcnow()
            approval["rejection_reason"] = reason
            
            self.logger.info("Request rejected", 
                           approval_id=approval_id,
                           approver=approver,
                           reason=reason)
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to reject request", approval_id=approval_id, error=str(e))
            return False
    
    async def _send_approval_notifications(self, approval_request: Dict[str, Any]) -> None:
        """Send approval notifications through configured channels."""
        # This would integrate with actual notification systems
        # For now, just log the notification
        self.logger.info("Approval notification sent", 
                        approval_id=approval_request["id"],
                        action=approval_request["action"],
                        channels=self.approval_channels)
    
    def get_pending_approvals(self) -> List[Dict[str, Any]]:
        """Get all pending approval requests."""
        pending = []
        for approval in self.pending_approvals.values():
            if approval["status"] == ApprovalStatus.PENDING:
                # Check if expired
                if datetime.utcnow() > approval["expires_at"]:
                    approval["status"] = ApprovalStatus.EXPIRED
                else:
                    pending.append(approval)
        
        return pending
    
    def cleanup_expired_approvals(self) -> int:
        """Clean up expired approval requests."""
        expired_count = 0
        current_time = datetime.utcnow()
        
        expired_ids = []
        for approval_id, approval in self.pending_approvals.items():
            if current_time > approval["expires_at"]:
                approval["status"] = ApprovalStatus.EXPIRED
                expired_ids.append(approval_id)
                expired_count += 1
        
        # Remove expired approvals after some time
        cleanup_time = current_time - timedelta(hours=24)
        for approval_id in list(self.pending_approvals.keys()):
            approval = self.pending_approvals[approval_id]
            if (approval["status"] == ApprovalStatus.EXPIRED and 
                approval["expires_at"] < cleanup_time):
                del self.pending_approvals[approval_id]
        
        return expired_count


class SafetyManager:
    """Main safety manager coordinating all safety mechanisms."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize safety manager."""
        self.config = config
        self.logger = logger.bind(component="safety_manager")
        
        # Initialize components
        self.rate_limiter = RateLimiter(
            max_requests=config.get("rate_limit_per_minute", 60),
            time_window=60
        )
        self.action_whitelist = ActionWhitelist(config.get("whitelist", {}))
        self.approval_manager = ApprovalManager(config.get("approval", {}))
        
        # Safety configuration
        self.enable_rate_limiting = config.get("enable_rate_limiting", True)
        self.enable_whitelisting = config.get("enable_whitelisting", True)
        self.enable_approvals = config.get("enable_approvals", True)
    
    async def is_action_safe(self, action: str, context: Dict[str, Any]) -> bool:
        """Check if an action is safe to execute."""
        try:
            # Check rate limiting
            if self.enable_rate_limiting:
                identifier = context.get("user_id", "default")
                if not self.rate_limiter.is_allowed(identifier):
                    self.logger.warning("Rate limit exceeded", action=action, identifier=identifier)
                    return False
            
            # Check action whitelist
            if self.enable_whitelisting:
                if not self.action_whitelist.is_action_allowed(action, context):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error("Error checking action safety", action=action, error=str(e))
            return False
    
    async def request_approval(self, action: str, context: Dict[str, Any]) -> bool:
        """Request approval for an action if needed."""
        if not self.enable_approvals:
            return True
        
        try:
            # Check if approval is required
            if not self.action_whitelist.requires_approval(action, context):
                return True
            
            # Request approval
            approval_id = await self.approval_manager.request_approval(action, context)
            
            # For now, return False to indicate approval is pending
            # In a real system, this would wait for approval or return the approval ID
            return False
            
        except Exception as e:
            self.logger.error("Error requesting approval", action=action, error=str(e))
            return False
    
    def get_safety_stats(self) -> Dict[str, Any]:
        """Get safety system statistics."""
        return {
            "rate_limiting_enabled": self.enable_rate_limiting,
            "whitelisting_enabled": self.enable_whitelisting,
            "approvals_enabled": self.enable_approvals,
            "pending_approvals": len(self.approval_manager.get_pending_approvals()),
            "allowed_actions": len(self.action_whitelist.allowed_actions),
            "restricted_actions": len(self.action_whitelist.restricted_actions),
            "high_risk_actions": len(self.action_whitelist.high_risk_actions)
        }
