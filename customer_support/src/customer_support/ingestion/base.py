"""Base classes for ticket ingestion."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Any, Optional
import structlog
from ..core.models import TicketCreate, Ticket, TicketModel, TicketStatus

logger = structlog.get_logger(__name__)


class TicketIngestionError(Exception):
    """Base exception for ticket ingestion errors."""
    pass


class BaseTicketIngester(ABC):
    """Base class for ticket ingestion from various sources."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the ingester with configuration."""
        self.config = config
        self.logger = logger.bind(ingester=self.__class__.__name__)
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the ticket source."""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from the ticket source."""
        pass
    
    @abstractmethod
    async def fetch_tickets(self, limit: Optional[int] = None) -> List[TicketCreate]:
        """Fetch new tickets from the source."""
        pass
    
    @abstractmethod
    async def acknowledge_ticket(self, external_id: str) -> bool:
        """Acknowledge that a ticket has been processed."""
        pass
    
    async def validate_ticket(self, ticket: TicketCreate) -> bool:
        """Validate a ticket before ingestion."""
        try:
            # Basic validation
            if not ticket.title or not ticket.title.strip():
                self.logger.warning("Ticket missing title", ticket_data=ticket.dict())
                return False
            
            if not ticket.description or not ticket.description.strip():
                self.logger.warning("Ticket missing description", ticket_data=ticket.dict())
                return False
            
            if not ticket.customer_email or "@" not in ticket.customer_email:
                self.logger.warning("Invalid customer email", email=ticket.customer_email)
                return False
            
            return True
        except Exception as e:
            self.logger.error("Error validating ticket", error=str(e), ticket_data=ticket.dict())
            return False
    
    async def preprocess_ticket(self, ticket: TicketCreate) -> TicketCreate:
        """Preprocess ticket data before ingestion."""
        # Clean and normalize data
        ticket.title = ticket.title.strip()
        ticket.description = ticket.description.strip()
        ticket.customer_email = ticket.customer_email.lower().strip()
        
        if ticket.customer_name:
            ticket.customer_name = ticket.customer_name.strip()
        
        # Add metadata if not present
        if not ticket.metadata:
            ticket.metadata = {}
        
        ticket.metadata["ingested_at"] = str(datetime.utcnow())
        ticket.metadata["ingester"] = self.__class__.__name__
        
        return ticket


class TicketProcessor:
    """Processes ingested tickets and stores them in the database."""
    
    def __init__(self, db_session):
        """Initialize with database session."""
        self.db = db_session
        self.logger = logger.bind(component="ticket_processor")
    
    async def process_ticket(self, ticket: TicketCreate) -> Optional[Ticket]:
        """Process and store a ticket in the database."""
        try:
            # Check for duplicates
            if ticket.external_id:
                existing = self.db.query(TicketModel).filter(
                    TicketModel.external_id == ticket.external_id
                ).first()
                if existing:
                    self.logger.info("Duplicate ticket found", external_id=ticket.external_id)
                    return None
            
            # Create database model
            db_ticket = TicketModel(
                external_id=ticket.external_id,
                title=ticket.title,
                description=ticket.description,
                status=TicketStatus.OPEN,
                priority=ticket.priority,
                source=ticket.source,
                customer_email=ticket.customer_email,
                customer_name=ticket.customer_name,
                metadata=ticket.metadata
            )
            
            self.db.add(db_ticket)
            self.db.commit()
            self.db.refresh(db_ticket)
            
            self.logger.info("Ticket processed successfully", ticket_id=db_ticket.id)
            
            # Convert to Pydantic model
            return Ticket.from_orm(db_ticket)
            
        except Exception as e:
            self.logger.error("Error processing ticket", error=str(e), ticket_data=ticket.dict())
            self.db.rollback()
            return None


class IngestionManager:
    """Manages multiple ticket ingesters."""
    
    def __init__(self, ingesters: List[BaseTicketIngester], processor: TicketProcessor):
        """Initialize with list of ingesters and processor."""
        self.ingesters = ingesters
        self.processor = processor
        self.logger = logger.bind(component="ingestion_manager")
    
    async def start_ingestion(self) -> None:
        """Start the ingestion process for all sources."""
        self.logger.info("Starting ticket ingestion", ingester_count=len(self.ingesters))
        
        for ingester in self.ingesters:
            try:
                await ingester.connect()
                self.logger.info("Connected to ingester", ingester=ingester.__class__.__name__)
            except Exception as e:
                self.logger.error(
                    "Failed to connect to ingester",
                    ingester=ingester.__class__.__name__,
                    error=str(e)
                )
    
    async def stop_ingestion(self) -> None:
        """Stop the ingestion process for all sources."""
        self.logger.info("Stopping ticket ingestion")
        
        for ingester in self.ingesters:
            try:
                await ingester.disconnect()
                self.logger.info("Disconnected from ingester", ingester=ingester.__class__.__name__)
            except Exception as e:
                self.logger.error(
                    "Error disconnecting from ingester",
                    ingester=ingester.__class__.__name__,
                    error=str(e)
                )
    
    async def ingest_batch(self, limit_per_source: Optional[int] = None) -> List[Ticket]:
        """Ingest a batch of tickets from all sources."""
        all_tickets = []
        
        for ingester in self.ingesters:
            try:
                # Fetch tickets from source
                raw_tickets = await ingester.fetch_tickets(limit=limit_per_source)
                self.logger.info(
                    "Fetched tickets from source",
                    ingester=ingester.__class__.__name__,
                    count=len(raw_tickets)
                )
                
                # Process each ticket
                for raw_ticket in raw_tickets:
                    # Validate ticket
                    if not await ingester.validate_ticket(raw_ticket):
                        continue
                    
                    # Preprocess ticket
                    processed_ticket = await ingester.preprocess_ticket(raw_ticket)
                    
                    # Store in database
                    ticket = await self.processor.process_ticket(processed_ticket)
                    if ticket:
                        all_tickets.append(ticket)
                        
                        # Acknowledge processing
                        if processed_ticket.external_id:
                            await ingester.acknowledge_ticket(processed_ticket.external_id)
                
            except Exception as e:
                self.logger.error(
                    "Error ingesting from source",
                    ingester=ingester.__class__.__name__,
                    error=str(e)
                )
        
        self.logger.info("Batch ingestion completed", total_tickets=len(all_tickets))
        return all_tickets
