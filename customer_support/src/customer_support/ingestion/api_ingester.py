"""API-based ticket ingestion from external helpdesk systems."""

import httpx
from datetime import datetime
from typing import List, Dict, Any, Optional
import structlog

from .base import BaseTicketIngester, TicketIngestionError
from ..core.models import TicketCreate, TicketSource, TicketPriority

logger = structlog.get_logger(__name__)


class APITicketIngester(BaseTicketIngester):
    """Ingests tickets from external helpdesk APIs."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize API ingester."""
        super().__init__(config)
        self.client: Optional[httpx.AsyncClient] = None
        
        # API configuration
        self.base_url = config.get("api_url")
        self.api_key = config.get("api_key")
        self.username = config.get("username")
        self.password = config.get("password")
        self.timeout = config.get("timeout", 30)
        self.headers = config.get("headers", {})
        
        # Endpoint configuration
        self.tickets_endpoint = config.get("tickets_endpoint", "/tickets")
        self.acknowledge_endpoint = config.get("acknowledge_endpoint", "/tickets/{id}/acknowledge")
        
        # Field mappings for different helpdesk systems
        self.field_mappings = config.get("field_mappings", {
            "id": "id",
            "title": "subject",
            "description": "description",
            "customer_email": "requester_email",
            "customer_name": "requester_name",
            "priority": "priority",
            "status": "status",
            "created_at": "created_at"
        })
        
        # Priority mappings
        self.priority_mappings = config.get("priority_mappings", {
            "low": TicketPriority.LOW,
            "normal": TicketPriority.MEDIUM,
            "medium": TicketPriority.MEDIUM,
            "high": TicketPriority.HIGH,
            "urgent": TicketPriority.URGENT,
            "critical": TicketPriority.URGENT
        })
    
    async def connect(self) -> bool:
        """Connect to the API."""
        try:
            # Setup authentication headers
            auth_headers = {}
            if self.api_key:
                auth_headers["Authorization"] = f"Bearer {self.api_key}"
            elif self.username and self.password:
                # Basic auth will be handled by httpx
                pass
            
            # Merge with custom headers
            headers = {**self.headers, **auth_headers}
            
            # Create HTTP client
            auth = None
            if self.username and self.password:
                auth = httpx.BasicAuth(self.username, self.password)
            
            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                headers=headers,
                auth=auth,
                timeout=self.timeout
            )
            
            # Test connection
            response = await self.client.get("/health", timeout=10)
            if response.status_code == 404:
                # Health endpoint might not exist, try tickets endpoint
                response = await self.client.get(self.tickets_endpoint, params={"limit": 1})
            
            if response.status_code < 400:
                self.logger.info("Connected to API", base_url=self.base_url)
                return True
            else:
                raise TicketIngestionError(f"API connection test failed: {response.status_code}")
                
        except Exception as e:
            self.logger.error("Failed to connect to API", error=str(e))
            raise TicketIngestionError(f"API connection failed: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the API."""
        if self.client:
            try:
                await self.client.aclose()
                self.logger.info("Disconnected from API")
            except Exception as e:
                self.logger.error("Error disconnecting from API", error=str(e))
            finally:
                self.client = None
    
    async def fetch_tickets(self, limit: Optional[int] = None) -> List[TicketCreate]:
        """Fetch new tickets from the API."""
        if not self.client:
            raise TicketIngestionError("Not connected to API")
        
        tickets = []
        
        try:
            # Prepare query parameters
            params = {
                "status": "new,open",  # Only fetch new/open tickets
                "sort": "created_at",
                "order": "desc"
            }
            
            if limit:
                params["limit"] = limit
            
            # Fetch tickets
            response = await self.client.get(self.tickets_endpoint, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Handle different response formats
            ticket_data = data
            if isinstance(data, dict):
                # Check common wrapper keys
                for key in ["tickets", "data", "results", "items"]:
                    if key in data:
                        ticket_data = data[key]
                        break
            
            if not isinstance(ticket_data, list):
                self.logger.error("Unexpected API response format", data_type=type(ticket_data))
                return tickets
            
            # Process each ticket
            for raw_ticket in ticket_data:
                try:
                    ticket = await self._process_api_ticket(raw_ticket)
                    if ticket:
                        tickets.append(ticket)
                except Exception as e:
                    self.logger.error("Error processing API ticket", ticket_id=raw_ticket.get("id"), error=str(e))
            
            self.logger.info("Fetched tickets from API", count=len(tickets))
            return tickets
            
        except httpx.HTTPStatusError as e:
            self.logger.error("API request failed", status_code=e.response.status_code, error=str(e))
            raise TicketIngestionError(f"API request failed: {e}")
        except Exception as e:
            self.logger.error("Error fetching tickets from API", error=str(e))
            raise TicketIngestionError(f"Failed to fetch tickets: {e}")
    
    async def _process_api_ticket(self, raw_ticket: Dict[str, Any]) -> Optional[TicketCreate]:
        """Process a single API ticket into a TicketCreate object."""
        try:
            # Map fields using field mappings
            mapped_data = {}
            for our_field, api_field in self.field_mappings.items():
                if api_field in raw_ticket:
                    mapped_data[our_field] = raw_ticket[api_field]
            
            # Extract required fields
            ticket_id = mapped_data.get("id")
            title = mapped_data.get("title", "No Title")
            description = mapped_data.get("description", "No Description")
            customer_email = mapped_data.get("customer_email")
            customer_name = mapped_data.get("customer_name")
            
            if not ticket_id or not customer_email:
                self.logger.warning("Missing required fields", ticket_data=mapped_data)
                return None
            
            # Map priority
            raw_priority = mapped_data.get("priority", "medium")
            if isinstance(raw_priority, str):
                priority = self.priority_mappings.get(raw_priority.lower(), TicketPriority.MEDIUM)
            else:
                priority = TicketPriority.MEDIUM
            
            # Create ticket
            ticket = TicketCreate(
                external_id=f"api_{ticket_id}",
                title=title,
                description=description,
                customer_email=customer_email,
                customer_name=customer_name,
                priority=priority,
                source=TicketSource.API,
                metadata={
                    "api_ticket_id": ticket_id,
                    "api_source": self.base_url,
                    "raw_data": raw_ticket,
                    "created_at": mapped_data.get("created_at")
                }
            )
            
            return ticket
            
        except Exception as e:
            self.logger.error("Error processing API ticket", error=str(e), raw_ticket=raw_ticket)
            return None
    
    async def acknowledge_ticket(self, external_id: str) -> bool:
        """Acknowledge that an API ticket has been processed."""
        if not self.client:
            return False
        
        try:
            # Extract ticket ID from external ID
            if external_id.startswith("api_"):
                ticket_id = external_id[4:]  # Remove "api_" prefix
                
                # Make acknowledgment request
                url = self.acknowledge_endpoint.format(id=ticket_id)
                response = await self.client.post(url)
                
                if response.status_code < 400:
                    self.logger.info("Acknowledged API ticket", ticket_id=ticket_id)
                    return True
                else:
                    self.logger.warning("Failed to acknowledge API ticket", 
                                      ticket_id=ticket_id, 
                                      status_code=response.status_code)
                    
        except Exception as e:
            self.logger.error("Error acknowledging API ticket", external_id=external_id, error=str(e))
        
        return False


class ZendeskIngester(APITicketIngester):
    """Specialized ingester for Zendesk API."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Zendesk ingester with specific configurations."""
        # Set Zendesk-specific defaults
        zendesk_config = {
            "tickets_endpoint": "/api/v2/tickets.json",
            "acknowledge_endpoint": "/api/v2/tickets/{id}/mark_as_spam.json",
            "field_mappings": {
                "id": "id",
                "title": "subject",
                "description": "description",
                "customer_email": "requester_email",
                "customer_name": "requester_name",
                "priority": "priority",
                "status": "status",
                "created_at": "created_at"
            },
            "headers": {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        }
        
        # Merge with user config
        merged_config = {**zendesk_config, **config}
        super().__init__(merged_config)


class FreshdeskIngester(APITicketIngester):
    """Specialized ingester for Freshdesk API."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Freshdesk ingester with specific configurations."""
        # Set Freshdesk-specific defaults
        freshdesk_config = {
            "tickets_endpoint": "/api/v2/tickets",
            "acknowledge_endpoint": "/api/v2/tickets/{id}",
            "field_mappings": {
                "id": "id",
                "title": "subject",
                "description": "description_text",
                "customer_email": "email",
                "customer_name": "name",
                "priority": "priority",
                "status": "status",
                "created_at": "created_at"
            },
            "priority_mappings": {
                1: TicketPriority.LOW,
                2: TicketPriority.MEDIUM,
                3: TicketPriority.HIGH,
                4: TicketPriority.URGENT
            }
        }
        
        # Merge with user config
        merged_config = {**freshdesk_config, **config}
        super().__init__(merged_config)
