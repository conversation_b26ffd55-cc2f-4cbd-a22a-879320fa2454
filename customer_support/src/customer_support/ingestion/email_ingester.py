"""Email-based ticket ingestion."""

import email
import imaplib
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import structlog

from .base import BaseTicket<PERSON>ngester, TicketIngestionError
from ..core.models import TicketCreate, TicketSource, TicketPriority

logger = structlog.get_logger(__name__)


class EmailTicketIngester(BaseTicketIngester):
    """Ingests tickets from email using IMAP."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize email ingester."""
        super().__init__(config)
        self.connection: Optional[imaplib.IMAP4_SSL] = None
        self.processed_emails = set()
        
        # Email configuration
        self.server = config.get("imap_server")
        self.port = config.get("imap_port", 993)
        self.username = config.get("username")
        self.password = config.get("password")
        self.use_ssl = config.get("use_ssl", True)
        self.mailbox = config.get("mailbox", "INBOX")
        self.mark_as_read = config.get("mark_as_read", True)
    
    async def connect(self) -> bool:
        """Connect to the email server."""
        try:
            if self.use_ssl:
                self.connection = imaplib.IMAP4_SSL(self.server, self.port)
            else:
                self.connection = imaplib.IMAP4(self.server, self.port)
            
            self.connection.login(self.username, self.password)
            self.connection.select(self.mailbox)
            
            self.logger.info("Connected to email server", server=self.server)
            return True
            
        except Exception as e:
            self.logger.error("Failed to connect to email server", error=str(e))
            raise TicketIngestionError(f"Email connection failed: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the email server."""
        if self.connection:
            try:
                self.connection.close()
                self.connection.logout()
                self.logger.info("Disconnected from email server")
            except Exception as e:
                self.logger.error("Error disconnecting from email server", error=str(e))
            finally:
                self.connection = None
    
    async def fetch_tickets(self, limit: Optional[int] = None) -> List[TicketCreate]:
        """Fetch new tickets from email."""
        if not self.connection:
            raise TicketIngestionError("Not connected to email server")
        
        tickets = []
        
        try:
            # Search for unread emails
            status, messages = self.connection.search(None, "UNSEEN")
            if status != "OK":
                self.logger.error("Failed to search emails")
                return tickets
            
            message_ids = messages[0].split()
            if limit:
                message_ids = message_ids[:limit]
            
            for msg_id in message_ids:
                try:
                    ticket = await self._process_email(msg_id)
                    if ticket:
                        tickets.append(ticket)
                except Exception as e:
                    self.logger.error("Error processing email", msg_id=msg_id, error=str(e))
            
            self.logger.info("Fetched tickets from email", count=len(tickets))
            return tickets
            
        except Exception as e:
            self.logger.error("Error fetching emails", error=str(e))
            raise TicketIngestionError(f"Failed to fetch emails: {e}")
    
    async def _process_email(self, msg_id: bytes) -> Optional[TicketCreate]:
        """Process a single email into a ticket."""
        try:
            # Fetch email
            status, msg_data = self.connection.fetch(msg_id, "(RFC822)")
            if status != "OK":
                return None
            
            # Parse email
            email_body = msg_data[0][1]
            email_message = email.message_from_bytes(email_body)
            
            # Extract email details
            subject = email_message.get("Subject", "No Subject")
            from_addr = email_message.get("From", "")
            date_str = email_message.get("Date", "")
            message_id = email_message.get("Message-ID", "")
            
            # Extract customer info
            customer_email = self._extract_email_address(from_addr)
            customer_name = self._extract_name(from_addr)
            
            # Extract email content
            content = self._extract_email_content(email_message)
            
            # Determine priority from subject
            priority = self._determine_priority(subject, content)
            
            # Create ticket
            ticket = TicketCreate(
                external_id=f"email_{message_id}",
                title=subject,
                description=content,
                customer_email=customer_email,
                customer_name=customer_name,
                priority=priority,
                source=TicketSource.EMAIL,
                metadata={
                    "email_date": date_str,
                    "email_message_id": message_id,
                    "email_from": from_addr,
                    "raw_subject": subject
                }
            )
            
            # Mark as read if configured
            if self.mark_as_read:
                self.connection.store(msg_id, "+FLAGS", "\\Seen")
            
            return ticket
            
        except Exception as e:
            self.logger.error("Error processing email", msg_id=msg_id, error=str(e))
            return None
    
    def _extract_email_address(self, from_field: str) -> str:
        """Extract email address from From field."""
        email_pattern = r'[\w\.-]+@[\w\.-]+\.\w+'
        match = re.search(email_pattern, from_field)
        return match.group(0) if match else from_field
    
    def _extract_name(self, from_field: str) -> Optional[str]:
        """Extract name from From field."""
        # Try to extract name before email address
        if "<" in from_field:
            name_part = from_field.split("<")[0].strip()
            # Remove quotes if present
            name_part = name_part.strip('"\'')
            return name_part if name_part else None
        return None
    
    def _extract_email_content(self, email_message) -> str:
        """Extract text content from email."""
        content = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    payload = part.get_payload(decode=True)
                    if payload:
                        content += payload.decode("utf-8", errors="ignore")
                elif content_type == "text/html" and not content:
                    # Fallback to HTML if no plain text
                    payload = part.get_payload(decode=True)
                    if payload:
                        # Simple HTML to text conversion
                        html_content = payload.decode("utf-8", errors="ignore")
                        content = re.sub(r'<[^>]+>', '', html_content)
        else:
            payload = email_message.get_payload(decode=True)
            if payload:
                content = payload.decode("utf-8", errors="ignore")
        
        # Clean up content
        content = content.strip()
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        return content or "No content available"
    
    def _determine_priority(self, subject: str, content: str) -> TicketPriority:
        """Determine ticket priority based on subject and content."""
        text = (subject + " " + content).lower()
        
        # Critical keywords
        if any(word in text for word in ["urgent", "critical", "emergency", "down", "outage"]):
            return TicketPriority.URGENT
        
        # High priority keywords
        if any(word in text for word in ["important", "asap", "priority", "issue", "problem"]):
            return TicketPriority.HIGH
        
        # Low priority keywords
        if any(word in text for word in ["question", "inquiry", "request", "info"]):
            return TicketPriority.LOW
        
        return TicketPriority.MEDIUM
    
    async def acknowledge_ticket(self, external_id: str) -> bool:
        """Acknowledge that an email ticket has been processed."""
        try:
            # Extract message ID from external ID
            if external_id.startswith("email_"):
                message_id = external_id[6:]  # Remove "email_" prefix
                self.processed_emails.add(message_id)
                self.logger.info("Acknowledged email ticket", message_id=message_id)
                return True
        except Exception as e:
            self.logger.error("Error acknowledging email ticket", external_id=external_id, error=str(e))
        
        return False
