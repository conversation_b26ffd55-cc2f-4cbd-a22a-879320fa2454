"""Base tool interface for agent actions."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import structlog
from datetime import datetime

from ..core.models import ActionResult

logger = structlog.get_logger(__name__)


class ToolError(Exception):
    """Base exception for tool errors."""
    pass


class BaseTool(ABC):
    """Base class for all agent tools."""
    
    def __init__(self, name: str, description: str, config: Dict[str, Any]):
        """Initialize the tool."""
        self.name = name
        self.description = description
        self.config = config
        self.logger = logger.bind(tool=name)
        
        # Tool metadata
        self.version = "1.0.0"
        self.category = "general"
        self.requires_approval = False
        self.risk_level = "low"  # low, medium, high
        self.execution_timeout = config.get("timeout", 30)  # seconds
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        """Execute the tool with given parameters."""
        pass
    
    @abstractmethod
    def validate_params(self, params: Dict[str, Any]) -> bool:
        """Validate tool parameters."""
        pass
    
    def get_required_params(self) -> List[str]:
        """Get list of required parameters."""
        return []
    
    def get_optional_params(self) -> List[str]:
        """Get list of optional parameters."""
        return []
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get tool metadata."""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "category": self.category,
            "requires_approval": self.requires_approval,
            "risk_level": self.risk_level,
            "execution_timeout": self.execution_timeout,
            "required_params": self.get_required_params(),
            "optional_params": self.get_optional_params()
        }
    
    async def safe_execute(self, params: Dict[str, Any]) -> ActionResult:
        """Execute the tool with safety checks and error handling."""
        start_time = datetime.now()
        
        try:
            # Validate parameters
            if not self.validate_params(params):
                return ActionResult(
                    success=False,
                    error_message="Invalid parameters provided",
                    execution_time=0
                )
            
            # Log execution start
            self.logger.info("Tool execution started", params=params)
            
            # Execute the tool
            result = await self.execute(params)
            
            # Calculate execution time
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            result.execution_time = execution_time
            
            # Log execution result
            self.logger.info("Tool execution completed", 
                           success=result.success,
                           execution_time=execution_time)
            
            return result
            
        except Exception as e:
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            self.logger.error("Tool execution failed", error=str(e), execution_time=execution_time)
            
            return ActionResult(
                success=False,
                error_message=f"Tool execution failed: {e}",
                execution_time=execution_time
            )


class TicketTool(BaseTool):
    """Base class for ticket-related tools."""
    
    def __init__(self, name: str, description: str, config: Dict[str, Any]):
        super().__init__(name, description, config)
        self.category = "ticket"
    
    def get_required_params(self) -> List[str]:
        return ["ticket"]


class KnowledgeTool(BaseTool):
    """Base class for knowledge-related tools."""
    
    def __init__(self, name: str, description: str, config: Dict[str, Any]):
        super().__init__(name, description, config)
        self.category = "knowledge"


class BrowserTool(BaseTool):
    """Base class for browser automation tools."""
    
    def __init__(self, name: str, description: str, config: Dict[str, Any]):
        super().__init__(name, description, config)
        self.category = "browser"
        self.risk_level = "medium"
        self.requires_approval = True


class APITool(BaseTool):
    """Base class for API interaction tools."""
    
    def __init__(self, name: str, description: str, config: Dict[str, Any]):
        super().__init__(name, description, config)
        self.category = "api"
        self.risk_level = "medium"
