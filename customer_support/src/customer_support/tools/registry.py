"""Tool registry for managing and executing agent tools."""

from typing import Dict, Any, List, Optional, Type
import structlog

from .base_tool import BaseTool
from ..core.models import ActionResult

logger = structlog.get_logger(__name__)


class ToolRegistryError(Exception):
    """Base exception for tool registry errors."""
    pass


class ToolRegistry:
    """Registry for managing agent tools."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the tool registry."""
        self.config = config
        self.logger = logger.bind(component="tool_registry")
        
        # Tool storage
        self.tools: Dict[str, BaseTool] = {}
        self.tool_categories: Dict[str, List[str]] = {}
        
        # Registry configuration
        self.max_tools = config.get("max_tools", 100)
        self.enable_tool_validation = config.get("enable_tool_validation", True)
    
    def register_tool(self, tool: BaseTool) -> bool:
        """Register a new tool."""
        try:
            # Check if tool already exists
            if tool.name in self.tools:
                self.logger.warning("Tool already registered", tool_name=tool.name)
                return False
            
            # Check tool limit
            if len(self.tools) >= self.max_tools:
                self.logger.error("Maximum tool limit reached", max_tools=self.max_tools)
                return False
            
            # Validate tool if enabled
            if self.enable_tool_validation:
                if not self._validate_tool(tool):
                    self.logger.error("Tool validation failed", tool_name=tool.name)
                    return False
            
            # Register the tool
            self.tools[tool.name] = tool
            
            # Update category mapping
            category = tool.category
            if category not in self.tool_categories:
                self.tool_categories[category] = []
            self.tool_categories[category].append(tool.name)
            
            self.logger.info("Tool registered successfully", 
                           tool_name=tool.name,
                           category=category,
                           risk_level=tool.risk_level)
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to register tool", tool_name=tool.name, error=str(e))
            return False
    
    def unregister_tool(self, tool_name: str) -> bool:
        """Unregister a tool."""
        try:
            if tool_name not in self.tools:
                self.logger.warning("Tool not found for unregistration", tool_name=tool_name)
                return False
            
            tool = self.tools[tool_name]
            category = tool.category
            
            # Remove from tools
            del self.tools[tool_name]
            
            # Remove from category mapping
            if category in self.tool_categories:
                self.tool_categories[category].remove(tool_name)
                if not self.tool_categories[category]:
                    del self.tool_categories[category]
            
            self.logger.info("Tool unregistered successfully", tool_name=tool_name)
            return True
            
        except Exception as e:
            self.logger.error("Failed to unregister tool", tool_name=tool_name, error=str(e))
            return False
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name."""
        return self.tools.get(tool_name)
    
    def list_tools(self, category: Optional[str] = None, 
                  risk_level: Optional[str] = None) -> List[str]:
        """List available tools with optional filtering."""
        tools = []
        
        for tool_name, tool in self.tools.items():
            # Filter by category
            if category and tool.category != category:
                continue
            
            # Filter by risk level
            if risk_level and tool.risk_level != risk_level:
                continue
            
            tools.append(tool_name)
        
        return tools
    
    def get_tool_metadata(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific tool."""
        tool = self.tools.get(tool_name)
        if tool:
            return tool.get_metadata()
        return None
    
    def get_all_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Get metadata for all registered tools."""
        metadata = {}
        for tool_name, tool in self.tools.items():
            metadata[tool_name] = tool.get_metadata()
        return metadata
    
    def get_tools_by_category(self, category: str) -> List[str]:
        """Get all tools in a specific category."""
        return self.tool_categories.get(category, [])
    
    def get_categories(self) -> List[str]:
        """Get all available tool categories."""
        return list(self.tool_categories.keys())
    
    async def execute_tool(self, tool_name: str, params: Dict[str, Any]) -> ActionResult:
        """Execute a tool with given parameters."""
        try:
            # Get the tool
            tool = self.tools.get(tool_name)
            if not tool:
                return ActionResult(
                    success=False,
                    error_message=f"Tool '{tool_name}' not found"
                )
            
            # Execute the tool safely
            result = await tool.safe_execute(params)
            
            self.logger.info("Tool executed", 
                           tool_name=tool_name,
                           success=result.success,
                           execution_time=result.execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error("Tool execution failed", tool_name=tool_name, error=str(e))
            return ActionResult(
                success=False,
                error_message=f"Tool execution failed: {e}"
            )
    
    def _validate_tool(self, tool: BaseTool) -> bool:
        """Validate a tool before registration."""
        try:
            # Check required attributes
            required_attrs = ["name", "description", "category", "version"]
            for attr in required_attrs:
                if not hasattr(tool, attr) or not getattr(tool, attr):
                    self.logger.error("Tool missing required attribute", 
                                    tool_name=tool.name, 
                                    missing_attr=attr)
                    return False
            
            # Check if tool implements required methods
            required_methods = ["execute", "validate_params"]
            for method in required_methods:
                if not hasattr(tool, method) or not callable(getattr(tool, method)):
                    self.logger.error("Tool missing required method", 
                                    tool_name=tool.name, 
                                    missing_method=method)
                    return False
            
            # Validate tool name format
            if not tool.name.replace("_", "").isalnum():
                self.logger.error("Invalid tool name format", tool_name=tool.name)
                return False
            
            return True
            
        except Exception as e:
            self.logger.error("Tool validation error", error=str(e))
            return False
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        stats = {
            "total_tools": len(self.tools),
            "categories": len(self.tool_categories),
            "category_breakdown": {
                category: len(tools) for category, tools in self.tool_categories.items()
            },
            "risk_level_breakdown": {},
            "approval_required_count": 0
        }
        
        # Calculate risk level breakdown
        risk_levels = {}
        approval_required = 0
        
        for tool in self.tools.values():
            risk_level = tool.risk_level
            risk_levels[risk_level] = risk_levels.get(risk_level, 0) + 1
            
            if tool.requires_approval:
                approval_required += 1
        
        stats["risk_level_breakdown"] = risk_levels
        stats["approval_required_count"] = approval_required
        
        return stats
    
    def export_tool_definitions(self) -> Dict[str, Any]:
        """Export tool definitions for external use."""
        definitions = {}
        
        for tool_name, tool in self.tools.items():
            definitions[tool_name] = {
                "name": tool.name,
                "description": tool.description,
                "category": tool.category,
                "version": tool.version,
                "risk_level": tool.risk_level,
                "requires_approval": tool.requires_approval,
                "required_params": tool.get_required_params(),
                "optional_params": tool.get_optional_params(),
                "execution_timeout": tool.execution_timeout
            }
        
        return definitions
    
    def clear_registry(self) -> None:
        """Clear all registered tools."""
        self.tools.clear()
        self.tool_categories.clear()
        self.logger.info("Tool registry cleared")


def create_default_registry(db_session, knowledge_base, browser_automator, 
                          api_connectors, config: Dict[str, Any]) -> ToolRegistry:
    """Create a registry with default tools."""
    registry = ToolRegistry(config)
    
    # Import tool classes
    from .ticket_tools import (
        GetTicketTool, UpdateTicketTool, PostReplyTool, 
        EscalateTicketTool, CloseTicketTool
    )
    from .knowledge_tools import (
        SearchKnowledgeTool, GetDocumentTool, ListCategoriesTools,
        GetSimilarDocumentsTool, GetKnowledgeStatsTool
    )
    from .browser_tools import (
        NavigateToUrlTool, LoginTool, ExtractDataTool, 
        UpdateStatusTool, PostReplyBrowserTool
    )
    from .api_tools import (
        GetTicketAPITool, UpdateTicketAPITool, PostReplyAPITool
    )
    
    # Register ticket tools
    registry.register_tool(GetTicketTool(db_session, config))
    registry.register_tool(UpdateTicketTool(db_session, config))
    registry.register_tool(PostReplyTool(db_session, config))
    registry.register_tool(EscalateTicketTool(db_session, config))
    registry.register_tool(CloseTicketTool(db_session, config))
    
    # Register knowledge tools
    registry.register_tool(SearchKnowledgeTool(knowledge_base, config))
    registry.register_tool(GetDocumentTool(knowledge_base, config))
    registry.register_tool(ListCategoriesTools(knowledge_base, config))
    registry.register_tool(GetSimilarDocumentsTool(knowledge_base, config))
    registry.register_tool(GetKnowledgeStatsTool(knowledge_base, config))
    
    # Register browser tools if available
    if browser_automator:
        registry.register_tool(NavigateToUrlTool(browser_automator, config))
        registry.register_tool(LoginTool(browser_automator, config))
        registry.register_tool(ExtractDataTool(browser_automator, config))
        registry.register_tool(UpdateStatusTool(browser_automator, config))
        registry.register_tool(PostReplyBrowserTool(browser_automator, config))
    
    # Register API tools if available
    for connector_name, connector in api_connectors.items():
        registry.register_tool(GetTicketAPITool(connector, config, connector_name))
        registry.register_tool(UpdateTicketAPITool(connector, config, connector_name))
        registry.register_tool(PostReplyAPITool(connector, config, connector_name))
    
    return registry
