"""Tools for ticket operations."""

from typing import Dict, Any, List
from datetime import datetime
from sqlalchemy.orm import Session

from .base_tool import TicketTool
from ..core.models import ActionResult, Ticket, TicketStatus, TicketUpdate, Message, MessageCreate
from ..automation.api_connector import BaseAPIConnector

import structlog

logger = structlog.get_logger(__name__)


class GetTicketTool(TicketTool):
    """Tool to retrieve ticket information."""
    
    def __init__(self, db_session: Session, config: Dict[str, Any]):
        super().__init__(
            name="get_ticket",
            description="Retrieve detailed information about a ticket",
            config=config
        )
        self.db = db_session
    
    def get_required_params(self) -> List[str]:
        return ["ticket_id"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return "ticket_id" in params and isinstance(params["ticket_id"], (int, str))
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            ticket_id = params["ticket_id"]
            
            # Get ticket from database
            from ..core.models import TicketModel
            db_ticket = self.db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
            
            if not db_ticket:
                return ActionResult(
                    success=False,
                    error_message=f"Ticket {ticket_id} not found"
                )
            
            # Convert to Pydantic model
            ticket = Ticket.from_orm(db_ticket)
            
            return ActionResult(
                success=True,
                output_data=ticket.dict()
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Failed to get ticket: {e}"
            )


class UpdateTicketTool(TicketTool):
    """Tool to update ticket information."""
    
    def __init__(self, db_session: Session, config: Dict[str, Any]):
        super().__init__(
            name="update_ticket",
            description="Update ticket status, priority, or other fields",
            config=config
        )
        self.db = db_session
        self.requires_approval = True
        self.risk_level = "medium"
    
    def get_required_params(self) -> List[str]:
        return ["ticket_id", "update_data"]
    
    def get_optional_params(self) -> List[str]:
        return ["reason"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if "ticket_id" not in params or "update_data" not in params:
            return False
        
        if not isinstance(params["update_data"], dict):
            return False
        
        # Validate update fields
        allowed_fields = ["status", "priority", "assigned_agent", "metadata"]
        update_data = params["update_data"]
        
        return all(field in allowed_fields for field in update_data.keys())
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            ticket_id = params["ticket_id"]
            update_data = params["update_data"]
            reason = params.get("reason", "Updated by AI agent")
            
            # Get ticket from database
            from ..core.models import TicketModel
            db_ticket = self.db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
            
            if not db_ticket:
                return ActionResult(
                    success=False,
                    error_message=f"Ticket {ticket_id} not found"
                )
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(db_ticket, field):
                    setattr(db_ticket, field, value)
            
            # Add update reason to metadata
            if not db_ticket.metadata:
                db_ticket.metadata = {}
            
            db_ticket.metadata["last_update_reason"] = reason
            db_ticket.metadata["last_updated_by"] = "ai_agent"
            
            self.db.commit()
            self.db.refresh(db_ticket)
            
            # Convert to Pydantic model
            ticket = Ticket.from_orm(db_ticket)
            
            return ActionResult(
                success=True,
                output_data={
                    "ticket": ticket.dict(),
                    "updated_fields": list(update_data.keys()),
                    "reason": reason
                }
            )
            
        except Exception as e:
            self.db.rollback()
            return ActionResult(
                success=False,
                error_message=f"Failed to update ticket: {e}"
            )


class PostReplyTool(TicketTool):
    """Tool to post a reply to a ticket."""
    
    def __init__(self, db_session: Session, config: Dict[str, Any]):
        super().__init__(
            name="post_reply",
            description="Post a reply message to a ticket",
            config=config
        )
        self.db = db_session
        self.requires_approval = False  # Replies are generally safe
        self.risk_level = "low"
    
    def get_required_params(self) -> List[str]:
        return ["ticket_id", "reply_text"]
    
    def get_optional_params(self) -> List[str]:
        return ["is_internal", "sender"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if "ticket_id" not in params or "reply_text" not in params:
            return False
        
        reply_text = params["reply_text"]
        if not isinstance(reply_text, str) or len(reply_text.strip()) == 0:
            return False
        
        return True
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            ticket_id = params["ticket_id"]
            reply_text = params["reply_text"].strip()
            is_internal = params.get("is_internal", False)
            sender = params.get("sender", "ai_agent")
            
            # Verify ticket exists
            from ..core.models import TicketModel, MessageModel
            db_ticket = self.db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
            
            if not db_ticket:
                return ActionResult(
                    success=False,
                    error_message=f"Ticket {ticket_id} not found"
                )
            
            # Create message
            message_data = MessageCreate(
                ticket_id=ticket_id,
                sender=sender,
                content=reply_text,
                is_internal=is_internal
            )
            
            db_message = MessageModel(
                ticket_id=message_data.ticket_id,
                sender=message_data.sender,
                content=message_data.content,
                is_internal=message_data.is_internal,
                metadata={"posted_by": "ai_agent"}
            )
            
            self.db.add(db_message)
            self.db.commit()
            self.db.refresh(db_message)
            
            # Convert to Pydantic model
            message = Message.from_orm(db_message)
            
            return ActionResult(
                success=True,
                output_data={
                    "message": message.dict(),
                    "ticket_id": ticket_id,
                    "reply_length": len(reply_text)
                }
            )
            
        except Exception as e:
            self.db.rollback()
            return ActionResult(
                success=False,
                error_message=f"Failed to post reply: {e}"
            )


class EscalateTicketTool(TicketTool):
    """Tool to escalate a ticket to human agents."""
    
    def __init__(self, db_session: Session, config: Dict[str, Any]):
        super().__init__(
            name="escalate_ticket",
            description="Escalate a ticket to human agents for manual handling",
            config=config
        )
        self.db = db_session
        self.requires_approval = True
        self.risk_level = "high"
    
    def get_required_params(self) -> List[str]:
        return ["ticket_id", "escalation_reason"]
    
    def get_optional_params(self) -> List[str]:
        return ["assigned_agent", "priority_override"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        required = ["ticket_id", "escalation_reason"]
        return all(param in params for param in required)
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            ticket_id = params["ticket_id"]
            escalation_reason = params["escalation_reason"]
            assigned_agent = params.get("assigned_agent")
            priority_override = params.get("priority_override")
            
            # Get ticket from database
            from ..core.models import TicketModel, MessageModel
            db_ticket = self.db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
            
            if not db_ticket:
                return ActionResult(
                    success=False,
                    error_message=f"Ticket {ticket_id} not found"
                )
            
            # Update ticket status
            db_ticket.status = TicketStatus.ESCALATED
            if assigned_agent:
                db_ticket.assigned_agent = assigned_agent
            if priority_override:
                db_ticket.priority = priority_override
            
            # Add escalation metadata
            if not db_ticket.metadata:
                db_ticket.metadata = {}
            
            db_ticket.metadata.update({
                "escalated_by": "ai_agent",
                "escalation_reason": escalation_reason,
                "escalated_at": str(datetime.utcnow()),
                "previous_status": db_ticket.status
            })
            
            # Add internal message about escalation
            escalation_message = MessageModel(
                ticket_id=ticket_id,
                sender="system",
                content=f"Ticket escalated by AI agent. Reason: {escalation_reason}",
                is_internal=True,
                metadata={"escalation": True}
            )
            
            self.db.add(escalation_message)
            self.db.commit()
            self.db.refresh(db_ticket)
            
            # Convert to Pydantic model
            ticket = Ticket.from_orm(db_ticket)
            
            return ActionResult(
                success=True,
                output_data={
                    "ticket": ticket.dict(),
                    "escalation_reason": escalation_reason,
                    "assigned_agent": assigned_agent
                }
            )
            
        except Exception as e:
            self.db.rollback()
            return ActionResult(
                success=False,
                error_message=f"Failed to escalate ticket: {e}"
            )


class CloseTicketTool(TicketTool):
    """Tool to close a resolved ticket."""
    
    def __init__(self, db_session: Session, config: Dict[str, Any]):
        super().__init__(
            name="close_ticket",
            description="Close a ticket that has been resolved",
            config=config
        )
        self.db = db_session
        self.requires_approval = True
        self.risk_level = "high"
    
    def get_required_params(self) -> List[str]:
        return ["ticket_id", "resolution_summary"]
    
    def get_optional_params(self) -> List[str]:
        return ["customer_satisfaction_survey"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        required = ["ticket_id", "resolution_summary"]
        return all(param in params for param in required)
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            ticket_id = params["ticket_id"]
            resolution_summary = params["resolution_summary"]
            
            # Get ticket from database
            from ..core.models import TicketModel, MessageModel
            db_ticket = self.db.query(TicketModel).filter(TicketModel.id == ticket_id).first()
            
            if not db_ticket:
                return ActionResult(
                    success=False,
                    error_message=f"Ticket {ticket_id} not found"
                )
            
            # Update ticket status
            db_ticket.status = TicketStatus.CLOSED
            db_ticket.resolved_at = datetime.utcnow()
            
            # Add resolution metadata
            if not db_ticket.metadata:
                db_ticket.metadata = {}
            
            db_ticket.metadata.update({
                "resolved_by": "ai_agent",
                "resolution_summary": resolution_summary,
                "closed_at": str(datetime.utcnow())
            })
            
            # Add internal message about closure
            closure_message = MessageModel(
                ticket_id=ticket_id,
                sender="system",
                content=f"Ticket closed by AI agent. Resolution: {resolution_summary}",
                is_internal=True,
                metadata={"closure": True}
            )
            
            self.db.add(closure_message)
            self.db.commit()
            self.db.refresh(db_ticket)
            
            # Convert to Pydantic model
            ticket = Ticket.from_orm(db_ticket)
            
            return ActionResult(
                success=True,
                output_data={
                    "ticket": ticket.dict(),
                    "resolution_summary": resolution_summary
                }
            )
            
        except Exception as e:
            self.db.rollback()
            return ActionResult(
                success=False,
                error_message=f"Failed to close ticket: {e}"
            )
