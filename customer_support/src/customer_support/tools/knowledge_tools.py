"""Tools for knowledge base operations."""

from typing import Dict, Any, List

from .base_tool import KnowledgeTool
from ..core.models import ActionResult, SearchQuery
from ..knowledge.knowledge_base import KnowledgeBase

import structlog

logger = structlog.get_logger(__name__)


class SearchKnowledgeTool(KnowledgeTool):
    """Tool to search the knowledge base."""
    
    def __init__(self, knowledge_base: KnowledgeBase, config: Dict[str, Any]):
        super().__init__(
            name="search_knowledge",
            description="Search the knowledge base for relevant information",
            config=config
        )
        self.knowledge_base = knowledge_base
    
    def get_required_params(self) -> List[str]:
        return ["query"]
    
    def get_optional_params(self) -> List[str]:
        return ["limit", "threshold", "category"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if "query" not in params:
            return False
        
        query = params["query"]
        if not isinstance(query, str) or len(query.strip()) == 0:
            return False
        
        return True
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            query_text = params["query"].strip()
            limit = params.get("limit", 5)
            threshold = params.get("threshold", 0.7)
            category = params.get("category")
            
            # Create search query
            search_query = SearchQuery(
                query=query_text,
                limit=limit,
                threshold=threshold,
                category=category
            )
            
            # Perform search
            results = await self.knowledge_base.search(search_query)
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "document_id": result.document.id,
                    "title": result.document.title,
                    "content": result.document.content[:500] + "..." if len(result.document.content) > 500 else result.document.content,
                    "category": result.document.category,
                    "score": result.score,
                    "relevance": result.relevance,
                    "source": result.document.source
                })
            
            return ActionResult(
                success=True,
                output_data={
                    "query": query_text,
                    "results": formatted_results,
                    "total_results": len(formatted_results)
                }
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Knowledge search failed: {e}"
            )


class GetDocumentTool(KnowledgeTool):
    """Tool to retrieve a specific knowledge document."""
    
    def __init__(self, knowledge_base: KnowledgeBase, config: Dict[str, Any]):
        super().__init__(
            name="get_document",
            description="Retrieve a specific knowledge base document by ID",
            config=config
        )
        self.knowledge_base = knowledge_base
    
    def get_required_params(self) -> List[str]:
        return ["document_id"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return "document_id" in params and isinstance(params["document_id"], (int, str))
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            document_id = int(params["document_id"])
            
            # Get document
            document = await self.knowledge_base.get_document(document_id)
            
            if not document:
                return ActionResult(
                    success=False,
                    error_message=f"Document {document_id} not found"
                )
            
            return ActionResult(
                success=True,
                output_data=document.dict()
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Failed to get document: {e}"
            )


class ListCategoriesTools(KnowledgeTool):
    """Tool to list available knowledge base categories."""
    
    def __init__(self, knowledge_base: KnowledgeBase, config: Dict[str, Any]):
        super().__init__(
            name="list_categories",
            description="List all available knowledge base categories",
            config=config
        )
        self.knowledge_base = knowledge_base
    
    def get_required_params(self) -> List[str]:
        return []
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return True  # No parameters required
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            categories = await self.knowledge_base.get_categories()
            
            return ActionResult(
                success=True,
                output_data={
                    "categories": categories,
                    "total_categories": len(categories)
                }
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Failed to list categories: {e}"
            )


class GetSimilarDocumentsTool(KnowledgeTool):
    """Tool to find documents similar to a given document."""
    
    def __init__(self, knowledge_base: KnowledgeBase, config: Dict[str, Any]):
        super().__init__(
            name="get_similar_documents",
            description="Find documents similar to a given document",
            config=config
        )
        self.knowledge_base = knowledge_base
    
    def get_required_params(self) -> List[str]:
        return ["document_id"]
    
    def get_optional_params(self) -> List[str]:
        return ["limit"]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return "document_id" in params and isinstance(params["document_id"], (int, str))
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            document_id = int(params["document_id"])
            limit = params.get("limit", 5)
            
            # Get similar documents
            results = await self.knowledge_base.embedding_manager.get_similar_documents(
                document_id, limit
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "document_id": result.document.id,
                    "title": result.document.title,
                    "content": result.document.content[:300] + "..." if len(result.document.content) > 300 else result.document.content,
                    "category": result.document.category,
                    "score": result.score,
                    "relevance": result.relevance
                })
            
            return ActionResult(
                success=True,
                output_data={
                    "source_document_id": document_id,
                    "similar_documents": formatted_results,
                    "total_results": len(formatted_results)
                }
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Failed to get similar documents: {e}"
            )


class GetKnowledgeStatsTool(KnowledgeTool):
    """Tool to get knowledge base statistics."""
    
    def __init__(self, knowledge_base: KnowledgeBase, config: Dict[str, Any]):
        super().__init__(
            name="get_knowledge_stats",
            description="Get statistics about the knowledge base",
            config=config
        )
        self.knowledge_base = knowledge_base
    
    def get_required_params(self) -> List[str]:
        return []
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return True  # No parameters required
    
    async def execute(self, params: Dict[str, Any]) -> ActionResult:
        try:
            stats = await self.knowledge_base.get_stats()
            
            return ActionResult(
                success=True,
                output_data=stats
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                error_message=f"Failed to get knowledge stats: {e}"
            )
