#!/usr/bin/env python3
"""Start the customer support agent application."""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all requirements are met."""
    print("Checking requirements...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("Please copy .env.example to .env and configure your settings.")
        return False
    
    # Check if required environment variables are set
    required_vars = [
        "GEMINI_API_KEY",
        "SECRET_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file.")
        return False
    
    print("✅ Requirements check passed!")
    return True


def install_playwright():
    """Install Playwright browsers."""
    print("Installing Playwright browsers...")
    try:
        subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
        print("✅ Playwright browsers installed!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")
        return False
    return True


def initialize_database():
    """Initialize the database."""
    print("Initializing database...")
    try:
        subprocess.run([sys.executable, "scripts/init_db.py"], check=True)
        print("✅ Database initialized!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to initialize database: {e}")
        return False
    return True


def start_application():
    """Start the FastAPI application."""
    print("Starting Customer Support Agent...")
    print("🚀 Application will be available at: http://localhost:8000")
    print("📚 API documentation: http://localhost:8000/docs")
    print("📊 Metrics: http://localhost:8000/metrics")
    print("\nPress Ctrl+C to stop the application.\n")
    
    try:
        # Change to the correct directory
        os.chdir(Path(__file__).parent.parent)
        
        # Start the application
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "src.customer_support.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Application failed to start: {e}")
        return False
    
    return True


def main():
    """Main startup function."""
    print("🤖 Customer Support Agent Startup")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Install Playwright browsers
    if not install_playwright():
        print("⚠️  Playwright installation failed, but continuing...")
    
    # Initialize database
    if not initialize_database():
        print("⚠️  Database initialization failed, but continuing...")
    
    # Start application
    if not start_application():
        sys.exit(1)


if __name__ == "__main__":
    main()
