#!/usr/bin/env python3
"""Initialize the database with sample data."""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from customer_support.config import settings
from customer_support.core.models import Base, TicketModel, MessageModel, KnowledgeDocumentModel
from customer_support.core.models import TicketStatus, TicketPriority, TicketSource


def create_database():
    """Create database tables."""
    print("Creating database tables...")
    
    engine = create_engine(settings.database_url)
    Base.metadata.create_all(bind=engine)
    
    print("Database tables created successfully!")
    return engine


def add_sample_data(engine):
    """Add sample data to the database."""
    print("Adding sample data...")
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Sample tickets
        sample_tickets = [
            {
                "external_id": "sample_001",
                "title": "Login Issues",
                "description": "I cannot log into my account. Getting error message 'Invalid credentials'.",
                "status": TicketStatus.OPEN,
                "priority": TicketPriority.MEDIUM,
                "source": TicketSource.EMAIL,
                "customer_email": "<EMAIL>",
                "customer_name": "John Doe",
                "metadata": {"sample": True}
            },
            {
                "external_id": "sample_002", 
                "title": "Billing Question",
                "description": "I was charged twice for my subscription this month. Please help.",
                "status": TicketStatus.OPEN,
                "priority": TicketPriority.HIGH,
                "source": TicketSource.WEB_FORM,
                "customer_email": "<EMAIL>",
                "customer_name": "Jane Smith",
                "metadata": {"sample": True}
            },
            {
                "external_id": "sample_003",
                "title": "Feature Request",
                "description": "It would be great to have dark mode in the application.",
                "status": TicketStatus.OPEN,
                "priority": TicketPriority.LOW,
                "source": TicketSource.API,
                "customer_email": "<EMAIL>",
                "customer_name": "Bob Wilson",
                "metadata": {"sample": True}
            }
        ]
        
        for ticket_data in sample_tickets:
            # Check if ticket already exists
            existing = db.query(TicketModel).filter(
                TicketModel.external_id == ticket_data["external_id"]
            ).first()
            
            if not existing:
                ticket = TicketModel(**ticket_data)
                db.add(ticket)
                db.commit()
                db.refresh(ticket)
                
                # Add sample message
                message = MessageModel(
                    ticket_id=ticket.id,
                    sender="customer",
                    content=ticket_data["description"],
                    is_internal=False,
                    metadata={"initial_message": True}
                )
                db.add(message)
        
        # Sample knowledge documents
        sample_docs = [
            {
                "title": "How to Reset Your Password",
                "content": """To reset your password:
1. Go to the login page
2. Click 'Forgot Password'
3. Enter your email address
4. Check your email for reset instructions
5. Follow the link in the email
6. Create a new password

If you don't receive the email, check your spam folder or contact support.""",
                "source": "help_docs",
                "category": "account",
                "tags": ["password", "reset", "login", "account"],
                "metadata": {"sample": True}
            },
            {
                "title": "Billing and Subscription FAQ",
                "content": """Common billing questions:

Q: When am I charged?
A: You are charged on the same date each month as your initial subscription.

Q: How do I update my payment method?
A: Go to Account Settings > Billing > Payment Methods

Q: Can I get a refund?
A: Refunds are available within 30 days of purchase for annual plans.

Q: Why was I charged twice?
A: This may happen if your payment failed and was retried. Contact support for assistance.""",
                "source": "help_docs", 
                "category": "billing",
                "tags": ["billing", "subscription", "payment", "refund"],
                "metadata": {"sample": True}
            },
            {
                "title": "Troubleshooting Login Issues",
                "content": """If you're having trouble logging in:

1. Check your email and password are correct
2. Ensure Caps Lock is off
3. Try clearing your browser cache and cookies
4. Disable browser extensions temporarily
5. Try a different browser or incognito mode
6. Check if your account is locked (you'll see a message)
7. Reset your password if needed

Common error messages:
- "Invalid credentials": Wrong email or password
- "Account locked": Too many failed attempts
- "Account not found": Email not registered""",
                "source": "help_docs",
                "category": "technical",
                "tags": ["login", "troubleshooting", "technical", "browser"],
                "metadata": {"sample": True}
            },
            {
                "title": "Feature Request Process",
                "content": """How to submit feature requests:

1. Check our roadmap to see if it's already planned
2. Search existing requests to avoid duplicates
3. Submit your request with:
   - Clear description of the feature
   - Use case and benefits
   - Any mockups or examples
4. Our team reviews all requests monthly
5. Popular requests are prioritized
6. You'll be notified of status updates

We appreciate your feedback and suggestions!""",
                "source": "help_docs",
                "category": "general",
                "tags": ["feature", "request", "feedback", "roadmap"],
                "metadata": {"sample": True}
            }
        ]
        
        for doc_data in sample_docs:
            # Check if document already exists
            existing = db.query(KnowledgeDocumentModel).filter(
                KnowledgeDocumentModel.title == doc_data["title"]
            ).first()
            
            if not existing:
                doc = KnowledgeDocumentModel(**doc_data)
                db.add(doc)
        
        db.commit()
        print("Sample data added successfully!")
        
    except Exception as e:
        print(f"Error adding sample data: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main initialization function."""
    print("Initializing Customer Support Agent Database...")
    
    # Create database
    engine = create_database()
    
    # Add sample data
    add_sample_data(engine)
    
    print("Database initialization completed!")
    print(f"Database URL: {settings.database_url}")
    print("\nNext steps:")
    print("1. Set up your .env file with API keys")
    print("2. Run: python -m uvicorn src.customer_support.main:app --reload")
    print("3. Visit http://localhost:8000/docs for API documentation")


if __name__ == "__main__":
    main()
