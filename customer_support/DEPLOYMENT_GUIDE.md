# Customer Support Agent - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Google Gemini API key
- (Optional) Email account for ticket ingestion
- (Optional) Helpdesk API credentials

### 1. Setup Environment

```bash
# Clone and navigate to the project
cd customer_support

# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install
```

### 3. Initialize Database

```bash
# Run database initialization script
python scripts/init_db.py
```

### 4. Start the Application

```bash
# Option 1: Use the startup script
python scripts/start_agent.py

# Option 2: Start manually
python -m uvicorn src.customer_support.main:app --reload
```

### 5. Access the Application

- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Metrics**: http://localhost:8000/metrics

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Set environment variables
export GEMINI_API_KEY="your_api_key"
export SECRET_KEY="your_secret_key"

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f customer-support-agent
```

### Services Included
- **Customer Support Agent**: Main application (port 8000)
- **Redis**: Caching and session storage (port 6379)
- **Prometheus**: Metrics collection (port 9090)
- **Grafana**: Metrics visualization (port 3000)

## 🔧 Configuration

### Required Environment Variables

```bash
# Gemini API
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# Security
SECRET_KEY=your_secret_key_here

# Database
DATABASE_URL=sqlite:///./data/customer_support.db
```

### Optional Configuration

```bash
# Email Ingestion
EMAIL_IMAP_SERVER=imap.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Helpdesk Integration
HELPDESK_API_URL=https://your-helpdesk.com/api
HELPDESK_API_KEY=your_helpdesk_api_key

# Safety Settings
ENABLE_HUMAN_APPROVAL=true
MAX_ACTIONS_PER_TICKET=10
RATE_LIMIT_PER_MINUTE=60
```

## 📊 Monitoring

### Prometheus Metrics
- Ticket processing metrics
- Agent performance metrics
- Knowledge base search metrics
- Error rates and response times

### Grafana Dashboards
Access Grafana at http://localhost:3000 (admin/admin) to view:
- Real-time ticket processing
- Agent utilization
- System performance
- Error tracking

## 🔒 Security Features

### Built-in Safety Mechanisms
- **Rate Limiting**: Prevents system abuse
- **Action Whitelists**: Only approved actions are executed
- **Human Approval**: Critical actions require confirmation
- **Audit Logging**: Complete action history
- **Input Validation**: All inputs are validated

### Configurable Restrictions
- Customer tier-based restrictions
- Time-based action limits
- Category-specific rules
- Risk level assessments

## 🛠 API Usage

### Process a Ticket
```bash
curl -X POST "http://localhost:8000/tickets/1/process" \
  -H "Content-Type: application/json"
```

### Search Knowledge Base
```bash
curl -X GET "http://localhost:8000/knowledge/search?q=login%20issues" \
  -H "Content-Type: application/json"
```

### Get Application Stats
```bash
curl -X GET "http://localhost:8000/stats" \
  -H "Content-Type: application/json"
```

## 🧪 Testing

### Run Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_agent.py -v
```

### Test Coverage
- Agent processing logic
- Knowledge base operations
- Safety mechanisms
- API endpoints
- Data models

## 🔄 Ticket Ingestion

### Supported Sources
1. **Email**: IMAP-based email monitoring
2. **API**: REST API integration with helpdesk systems
3. **Web Form**: Direct API submissions
4. **Manual**: Admin interface

### Helpdesk Integrations
- **Zendesk**: Full API integration
- **Freshdesk**: Complete support
- **Generic REST**: Configurable field mappings

## 🧠 Knowledge Base

### Document Management
- Vector-based similarity search
- Category organization
- Automatic embedding generation
- Real-time updates

### Supported Formats
- Plain text documents
- Markdown files
- FAQ entries
- Policy documents

## 🤖 Agent Capabilities

### Core Features
- **Intelligent Analysis**: Categorizes and prioritizes tickets
- **Context Retrieval**: Searches knowledge base for relevant information
- **Response Generation**: Composes helpful customer replies
- **Action Planning**: Determines appropriate next steps
- **Safety Checks**: Validates all actions before execution

### Browser Automation
- Web-based helpdesk interactions
- Form filling and submission
- Data extraction
- Screenshot capture

## 📈 Performance Optimization

### Recommended Settings
- **Production**: Use PostgreSQL instead of SQLite
- **Scaling**: Deploy multiple worker processes
- **Caching**: Enable Redis for session storage
- **Monitoring**: Set up proper logging and alerting

### Resource Requirements
- **Minimum**: 2GB RAM, 2 CPU cores
- **Recommended**: 4GB RAM, 4 CPU cores
- **Storage**: 10GB for database and logs

## 🚨 Troubleshooting

### Common Issues

1. **Gemini API Errors**
   - Check API key validity
   - Verify rate limits
   - Review request format

2. **Database Connection Issues**
   - Check database URL
   - Verify file permissions
   - Ensure directory exists

3. **Browser Automation Failures**
   - Install Playwright browsers
   - Check system dependencies
   - Verify headless mode settings

4. **Email Ingestion Problems**
   - Verify IMAP credentials
   - Check firewall settings
   - Test email server connectivity

### Logs and Debugging
- Application logs: Check console output
- Structured logging: JSON format for analysis
- Error tracking: Automatic error reporting
- Performance metrics: Real-time monitoring

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Test with sample data
4. Verify configuration settings

## 🔄 Updates and Maintenance

### Regular Tasks
- Update knowledge base documents
- Review and approve pending actions
- Monitor system performance
- Update API credentials as needed

### Backup Recommendations
- Database: Regular automated backups
- Configuration: Version control
- Logs: Centralized log management
- Knowledge Base: Export and backup

---

**🎉 Your AI-powered customer support agent is ready to help your customers!**
