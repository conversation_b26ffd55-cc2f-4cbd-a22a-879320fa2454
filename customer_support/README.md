# Customer Support Agent

An AI-powered customer support agent built with Gemini API and BrowserUse for automated ticket handling and customer service.

## Features

- **Multi-source Ticket Ingestion**: Supports web UI, API, email, and helpdesk database integration
- **Browser Automation**: Uses BrowserUse/Playwright for web-based ticket management
- **Knowledge Base**: RAG-powered system with vector embeddings for context retrieval
- **AI Agent**: Gemini API-powered reasoning and response generation
- **Safety & Guardrails**: Human-in-the-loop approval, action whitelists, and rate limiting
- **Monitoring**: Comprehensive logging and telemetry for performance tracking

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Ticket        │    │   Browser       │    │   Knowledge     │
│   Ingestion     │    │   Automation    │    │   Layer (RAG)   │
│                 │    │                 │    │                 │
│ • Web UI        │    │ • BrowserUse    │    │ • FAISS         │
│ • API           │    │ • Playwright    │    │ • Embeddings    │
│ • Email         │    │ • Selenium      │    │ • Vector DB     │
│ • Helpdesk DB   │    │ • API Calls     │    │ • Documents     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   LLM Agent     │
                    │   (Gemini API)  │
                    │                 │
                    │ • Reasoning     │
                    │ • Context       │
                    │ • Actions       │
                    │ • Responses     │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Safety &      │    │   Tooling &     │    │   Monitoring    │
│   Guardrails    │    │   Orchestration │    │   & Telemetry   │
│                 │    │                 │    │                 │
│ • Whitelists    │    │ • Tool Registry │    │ • Logging       │
│ • Approvals     │    │ • Orchestrator  │    │ • Metrics       │
│ • Rate Limits   │    │ • Safe Tools    │    │ • Tracing       │
│ • Auditing      │    │ • Validation    │    │ • Analytics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository>
   cd customer_support
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install
   ```

3. **Initialize Database**:
   ```bash
   python scripts/init_db.py
   ```

4. **Start the Service**:
   ```bash
   python -m uvicorn src.customer_support.main:app --reload
   ```

## Configuration

Edit the `.env` file with your specific configuration:

- **Gemini API**: Get your API key from Google AI Studio
- **Database**: Configure your database connection
- **Email**: Set up email credentials for ticket ingestion
- **Helpdesk**: Configure your helpdesk system API
- **Security**: Set strong secret keys and passwords

## Usage

### API Endpoints

- `POST /tickets/ingest` - Ingest new tickets
- `GET /tickets/{ticket_id}` - Get ticket details
- `POST /tickets/{ticket_id}/process` - Process ticket with AI agent
- `GET /knowledge/search` - Search knowledge base
- `POST /knowledge/update` - Update knowledge base

### Web Interface

Access the web interface at `http://localhost:8000` for:
- Ticket management dashboard
- Agent performance monitoring
- Knowledge base management
- System configuration

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black src/ tests/
flake8 src/ tests/
mypy src/
```

### Adding New Tools

1. Create tool in `src/customer_support/tools/`
2. Register in `src/customer_support/tools/registry.py`
3. Add safety checks in `src/customer_support/safety/`
4. Update documentation

## Monitoring

The system provides comprehensive monitoring:

- **Prometheus Metrics**: Available at `:9090/metrics`
- **Structured Logs**: JSON format with correlation IDs
- **Performance Tracking**: Response times, success rates
- **Agent Analytics**: Decision patterns, tool usage

## Safety Features

- **Action Whitelists**: Only approved actions are executed
- **Human Approval**: Critical actions require human confirmation
- **Rate Limiting**: Prevents system abuse
- **Audit Logging**: Complete action history
- **Rollback Capability**: Undo problematic changes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
