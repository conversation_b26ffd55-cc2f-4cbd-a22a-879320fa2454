[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "customer-support-agent"
version = "0.1.0"
description = "AI-powered customer support agent using Gemini API and BrowserUse"
authors = [{name = "Customer Support Team", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "google-generativeai>=0.3.0",
    "browseruse>=0.1.0",
    "playwright>=1.40.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "redis>=5.0.0",
    "faiss-cpu>=1.7.4",
    "sentence-transformers>=2.2.2",
    "chromadb>=0.4.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "selenium>=4.15.0",
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    "cryptography>=41.0.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "httpx>=0.25.0",
    "tenacity>=8.2.3",
    "schedule>=1.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
]

[project.urls]
Homepage = "https://github.com/company/customer-support-agent"
Repository = "https://github.com/company/customer-support-agent"
Issues = "https://github.com/company/customer-support-agent/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
