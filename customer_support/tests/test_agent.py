"""Tests for the customer support agent."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from customer_support.core.models import (
    Ticket, TicketCreate, TicketStatus, TicketPriority, TicketSource,
    Message, MessageCreate, SearchQuery
)
from customer_support.agent.gemini_client import Gemini<PERSON><PERSON>
from customer_support.agent.support_agent import SupportAgent
from customer_support.knowledge.knowledge_base import KnowledgeBase
from customer_support.tools.registry import ToolRegistry
from customer_support.safety.guardrails import SafetyManager


@pytest.fixture
def mock_gemini_client():
    """Mock Gemini client."""
    client = Mock(spec=GeminiClient)
    client.analyze_ticket = AsyncMock(return_value={
        "category": "technical",
        "urgency": 3,
        "complexity": "medium",
        "estimated_resolution_time": "1-2 hours",
        "required_expertise": "technical",
        "key_issues": ["login_error"],
        "recommended_actions": ["check_credentials"],
        "confidence": 0.8
    })
    client.compose_reply = AsyncMock(return_value="Thank you for contacting us. We'll help you resolve this issue.")
    client.suggest_actions = AsyncMock(return_value=[
        {
            "action": "search_knowledge",
            "priority": 1,
            "reason": "Find relevant documentation",
            "expected_outcome": "Provide helpful information"
        }
    ])
    return client


@pytest.fixture
def mock_knowledge_base():
    """Mock knowledge base."""
    kb = Mock(spec=KnowledgeBase)
    kb.search = AsyncMock(return_value=[])
    return kb


@pytest.fixture
def mock_tool_registry():
    """Mock tool registry."""
    registry = Mock(spec=ToolRegistry)
    registry.list_tools = Mock(return_value=["search_knowledge", "post_reply", "update_ticket"])
    registry.get_tool = Mock(return_value=None)
    return registry


@pytest.fixture
def mock_safety_manager():
    """Mock safety manager."""
    safety = Mock(spec=SafetyManager)
    safety.is_action_safe = AsyncMock(return_value=True)
    safety.request_approval = AsyncMock(return_value=True)
    return safety


@pytest.fixture
def support_agent(mock_gemini_client, mock_knowledge_base, mock_tool_registry, mock_safety_manager):
    """Create support agent with mocked dependencies."""
    return SupportAgent(
        gemini_client=mock_gemini_client,
        knowledge_base=mock_knowledge_base,
        tool_registry=mock_tool_registry,
        safety_manager=mock_safety_manager,
        config={
            "max_actions_per_ticket": 10,
            "confidence_threshold": 0.7
        }
    )


@pytest.fixture
def sample_ticket():
    """Sample ticket for testing."""
    return Ticket(
        id=1,
        external_id="test_001",
        title="Login Issues",
        description="Cannot log into my account",
        status=TicketStatus.OPEN,
        priority=TicketPriority.MEDIUM,
        source=TicketSource.EMAIL,
        customer_email="<EMAIL>",
        customer_name="Test User",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )


@pytest.mark.asyncio
async def test_process_ticket(support_agent, sample_ticket):
    """Test ticket processing."""
    result = await support_agent.process_ticket(sample_ticket)
    
    assert result is not None
    assert "ticket_id" in result
    assert "analysis" in result
    assert "can_auto_handle" in result
    assert "action_plan" in result
    
    assert result["ticket_id"] == sample_ticket.id
    assert isinstance(result["can_auto_handle"], bool)
    assert isinstance(result["action_plan"], list)


@pytest.mark.asyncio
async def test_compose_reply(support_agent, sample_ticket):
    """Test reply composition."""
    reply = await support_agent.compose_reply(sample_ticket)
    
    assert isinstance(reply, str)
    assert len(reply) > 0
    assert "Thank you" in reply


@pytest.mark.asyncio
async def test_gather_knowledge_context(support_agent, sample_ticket):
    """Test knowledge context gathering."""
    # This tests the private method indirectly through process_ticket
    result = await support_agent.process_ticket(sample_ticket)
    
    # Verify that knowledge base search was called
    support_agent.knowledge_base.search.assert_called_once()
    
    # Check that search query was properly formed
    call_args = support_agent.knowledge_base.search.call_args[0][0]
    assert isinstance(call_args, SearchQuery)
    assert sample_ticket.title in call_args.query
    assert sample_ticket.description in call_args.query


@pytest.mark.asyncio
async def test_can_auto_handle_high_confidence(support_agent, sample_ticket):
    """Test auto-handling decision with high confidence."""
    # Mock high confidence analysis
    support_agent.gemini_client.analyze_ticket.return_value = {
        "category": "general",
        "urgency": 2,
        "complexity": "simple",
        "confidence": 0.9
    }
    
    result = await support_agent.process_ticket(sample_ticket)
    
    # Should be able to auto-handle with high confidence, low urgency, simple complexity
    assert result["can_auto_handle"] is True


@pytest.mark.asyncio
async def test_can_auto_handle_low_confidence(support_agent, sample_ticket):
    """Test auto-handling decision with low confidence."""
    # Mock low confidence analysis
    support_agent.gemini_client.analyze_ticket.return_value = {
        "category": "general",
        "urgency": 2,
        "complexity": "simple",
        "confidence": 0.5
    }
    
    result = await support_agent.process_ticket(sample_ticket)
    
    # Should not auto-handle with low confidence
    assert result["can_auto_handle"] is False


@pytest.mark.asyncio
async def test_can_auto_handle_high_urgency(support_agent, sample_ticket):
    """Test auto-handling decision with high urgency."""
    # Mock high urgency analysis
    support_agent.gemini_client.analyze_ticket.return_value = {
        "category": "general",
        "urgency": 5,
        "complexity": "simple",
        "confidence": 0.9
    }
    
    result = await support_agent.process_ticket(sample_ticket)
    
    # Should not auto-handle critical urgency tickets
    assert result["can_auto_handle"] is False


@pytest.mark.asyncio
async def test_can_auto_handle_complex_ticket(support_agent, sample_ticket):
    """Test auto-handling decision with complex ticket."""
    # Mock complex ticket analysis
    support_agent.gemini_client.analyze_ticket.return_value = {
        "category": "general",
        "urgency": 2,
        "complexity": "complex",
        "confidence": 0.9
    }
    
    result = await support_agent.process_ticket(sample_ticket)
    
    # Should not auto-handle complex tickets
    assert result["can_auto_handle"] is False


@pytest.mark.asyncio
async def test_can_auto_handle_restricted_category(support_agent, sample_ticket):
    """Test auto-handling decision with restricted category."""
    # Mock restricted category analysis
    support_agent.gemini_client.analyze_ticket.return_value = {
        "category": "billing",
        "urgency": 2,
        "complexity": "simple",
        "confidence": 0.9
    }
    
    # Update config to restrict billing category
    support_agent.config["restricted_categories"] = ["billing", "legal"]
    
    result = await support_agent.process_ticket(sample_ticket)
    
    # Should not auto-handle restricted categories
    assert result["can_auto_handle"] is False


@pytest.mark.asyncio
async def test_generate_action_plan(support_agent, sample_ticket):
    """Test action plan generation."""
    result = await support_agent.process_ticket(sample_ticket)
    
    action_plan = result["action_plan"]
    assert isinstance(action_plan, list)
    assert len(action_plan) > 0
    
    # Check action plan structure
    for action in action_plan:
        assert "action" in action
        assert "priority" in action
        assert "reason" in action
        assert "expected_outcome" in action
        assert "requires_approval" in action


@pytest.mark.asyncio
async def test_agent_stats(support_agent):
    """Test agent statistics."""
    stats = await support_agent.get_agent_stats()
    
    assert isinstance(stats, dict)
    assert "agent_version" in stats
    assert "max_actions_per_ticket" in stats
    assert "confidence_threshold" in stats
    assert "available_tools" in stats
    assert "status" in stats
    
    assert stats["status"] == "active"


def test_ticket_create_model():
    """Test ticket creation model."""
    ticket_data = TicketCreate(
        title="Test Ticket",
        description="Test description",
        customer_email="<EMAIL>",
        customer_name="Test User",
        priority=TicketPriority.MEDIUM,
        source=TicketSource.EMAIL
    )
    
    assert ticket_data.title == "Test Ticket"
    assert ticket_data.description == "Test description"
    assert ticket_data.customer_email == "<EMAIL>"
    assert ticket_data.priority == TicketPriority.MEDIUM
    assert ticket_data.source == TicketSource.EMAIL


def test_message_create_model():
    """Test message creation model."""
    message_data = MessageCreate(
        ticket_id=1,
        sender="customer",
        content="Test message content",
        is_internal=False
    )
    
    assert message_data.ticket_id == 1
    assert message_data.sender == "customer"
    assert message_data.content == "Test message content"
    assert message_data.is_internal is False


def test_search_query_model():
    """Test search query model."""
    query = SearchQuery(
        query="login issues",
        limit=5,
        threshold=0.7,
        category="technical"
    )
    
    assert query.query == "login issues"
    assert query.limit == 5
    assert query.threshold == 0.7
    assert query.category == "technical"


if __name__ == "__main__":
    pytest.main([__file__])
